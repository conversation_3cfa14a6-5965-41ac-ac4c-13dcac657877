'use client';

import { useState, useEffect } from 'react';
import { ref, onValue } from 'firebase/database';
import { database } from '@/lib/firebase';
import { Phone } from '@/types/phone';
import Navigation from '@/components/Navigation';
import HeroCarousel from '@/components/HeroCarousel';
import PhoneCard from '@/components/PhoneCard';
import { FiMapPin, FiPhone, FiMail, FiClock } from 'react-icons/fi';

export default function Home() {
  const [phones, setPhones] = useState<Phone[]>([]);
  const [filteredPhones, setFilteredPhones] = useState<Phone[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('featured');

  // Fetch phones from Firebase
  useEffect(() => {
    const phonesRef = ref(database, 'phones');
    const unsubscribe = onValue(phonesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const phonesList = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));
        setPhones(phonesList);
        setFilteredPhones(phonesList);
      } else {
        // Demo data if no Firebase data
        const demoPhones: Phone[] = [
          {
            id: '1',
            name: 'iPhone 15 Pro Max',
            storage: '256GB',
            price: 180000,
            description: 'Latest iPhone with titanium design and advanced camera system',
            imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
            featured: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '2',
            name: 'Samsung Galaxy S24 Ultra',
            storage: '512GB',
            price: 165000,
            description: 'Premium Android flagship with S Pen and incredible camera zoom',
            imageUrl: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
            featured: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '3',
            name: 'Google Pixel 8 Pro',
            storage: '128GB',
            price: 120000,
            description: 'Pure Android experience with exceptional AI photography',
            imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '4',
            name: 'OnePlus 12',
            storage: '256GB',
            price: 95000,
            description: 'Fast charging flagship with premium performance',
            imageUrl: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=500',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ];
        setPhones(demoPhones);
        setFilteredPhones(demoPhones);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Filter and sort phones
  useEffect(() => {
    let filtered = phones;

    // Apply search filter
    if (searchQuery.trim() !== '') {
      filtered = filtered.filter(phone =>
        phone.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        phone.storage.toLowerCase().includes(searchQuery.toLowerCase()) ||
        phone.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory && selectedCategory !== 'Deals') {
      filtered = filtered.filter(phone =>
        phone.name.toLowerCase().includes(selectedCategory.toLowerCase())
      );
    } else if (selectedCategory === 'Deals') {
      filtered = filtered.filter(phone => phone.price < 100000);
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'featured':
      default:
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return 0;
        });
        break;
    }

    setFilteredPhones(filtered);
  }, [searchQuery, selectedCategory, sortBy, phones]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category);
  };

  const featuredPhones = phones.filter(phone => phone.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation onSearch={handleSearch} onCategoryFilter={handleCategoryFilter} />

      {/* Hero Section */}
      <HeroCarousel featuredPhones={featuredPhones} />

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About High End Phones.ke
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We are Kenya's premier destination for high-end smartphones. Our commitment to quality,
              competitive pricing, and exceptional customer service makes us the trusted choice for
              premium mobile devices.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiPhone className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Premium Quality</h3>
              <p className="text-gray-600">Only the finest smartphones from trusted brands</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiMapPin className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Kenya-wide Delivery</h3>
              <p className="text-gray-600">Fast and secure delivery across Kenya</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiClock className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
              <p className="text-gray-600">Round-the-clock customer support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Phones Section */}
      <section id="phones" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Premium Collection
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the latest high-end smartphones with cutting-edge technology and unmatched performance
            </p>
          </div>

          {/* Filters and Sort */}
          <div className="flex flex-col lg:flex-row justify-between items-center mb-12 space-y-4 lg:space-y-0">
            {/* Category Pills */}
            <div className="flex flex-wrap gap-3">
              {['iPhone', 'Samsung', 'Google', 'OnePlus', 'Deals'].map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryFilter(category)}
                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'gradient-gold text-black shadow-gold'
                      : 'glass hover:bg-white/20 text-gray-700'
                  }`}
                >
                  {category === 'Deals' ? '🔥 ' : ''}
                  {category}
                </button>
              ))}
              {selectedCategory && (
                <button
                  onClick={() => setSelectedCategory('')}
                  className="px-4 py-3 rounded-full text-gray-500 hover:text-gray-700 transition-colors duration-300"
                >
                  ✕ Clear
                </button>
              )}
            </div>

            {/* Sort Dropdown */}
            <div className="flex items-center space-x-4">
              <span className="text-gray-600 font-medium">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="glass rounded-lg px-4 py-2 font-medium focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              >
                <option value="featured">Featured</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="name">Name A-Z</option>
              </select>
            </div>
          </div>

          {/* Results Count */}
          {!loading && (
            <div className="mb-8">
              <p className="text-gray-600">
                {selectedCategory && (
                  <span className="font-medium text-yellow-600">{selectedCategory} • </span>
                )}
                {filteredPhones.length} phone{filteredPhones.length !== 1 ? 's' : ''} found
                {searchQuery && (
                  <span className="ml-2 text-sm">
                    for "<span className="font-medium">{searchQuery}</span>"
                  </span>
                )}
              </p>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <PhoneCard key={i} phone={{} as Phone} isLoading={true} />
              ))}
            </div>
          ) : (
            <>
              {/* Phones Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {filteredPhones.map((phone) => (
                  <PhoneCard key={phone.id} phone={phone} />
                ))}
              </div>

              {/* Empty State */}
              {filteredPhones.length === 0 && (
                <div className="text-center py-20">
                  <div className="max-w-md mx-auto">
                    <div className="text-6xl mb-6">📱</div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">No phones found</h3>
                    <p className="text-gray-600 mb-8">
                      {searchQuery || selectedCategory
                        ? "Try adjusting your search or filters to find what you're looking for."
                        : "We're currently updating our inventory. Please check back soon!"}
                    </p>
                    {(searchQuery || selectedCategory) && (
                      <button
                        onClick={() => {
                          setSearchQuery('');
                          setSelectedCategory('');
                        }}
                        className="gradient-gold text-black font-semibold py-3 px-8 rounded-full hover-glow transition-all duration-300"
                      >
                        Clear All Filters
                      </button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get in Touch
            </h2>
            <p className="text-lg text-gray-600">
              Ready to upgrade your phone? Contact us today!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <FiPhone className="text-yellow-600 text-xl" />
                  <span className="text-gray-700">+254 700 000 000</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiMail className="text-yellow-600 text-xl" />
                  <span className="text-gray-700"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiMapPin className="text-yellow-600 text-xl" />
                  <span className="text-gray-700">Nairobi, Kenya</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-2xl font-semibold mb-6">Business Hours</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-700">Monday - Friday</span>
                  <span className="text-gray-900 font-medium">9:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-700">Saturday</span>
                  <span className="text-gray-900 font-medium">10:00 AM - 4:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-700">Sunday</span>
                  <span className="text-gray-900 font-medium">Closed</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gradient mb-4">High End Phones.ke</h3>
            <p className="text-gray-400 mb-4">
              Your trusted partner for premium smartphones in Kenya
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 High End Phones.ke. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
