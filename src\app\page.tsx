'use client';

import { useState, useEffect } from 'react';
import { ref, onValue } from 'firebase/database';
import { database } from '@/lib/firebase';
import { Phone } from '@/types/phone';
import Navigation from '@/components/Navigation';
import HeroCarousel from '@/components/HeroCarousel';
import PhoneCard from '@/components/PhoneCard';
import { FiMapPin, FiPhone, FiMail, FiClock } from 'react-icons/fi';

export default function Home() {
  const [phones, setPhones] = useState<Phone[]>([]);
  const [filteredPhones, setFilteredPhones] = useState<Phone[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch phones from Firebase
  useEffect(() => {
    const phonesRef = ref(database, 'phones');
    const unsubscribe = onValue(phonesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const phonesList = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));
        setPhones(phonesList);
        setFilteredPhones(phonesList);
      } else {
        // Demo data if no Firebase data
        const demoPhones: Phone[] = [
          {
            id: '1',
            name: 'iPhone 15 Pro Max',
            storage: '256GB',
            price: 180000,
            description: 'Latest iPhone with titanium design and advanced camera system',
            imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
            featured: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '2',
            name: 'Samsung Galaxy S24 Ultra',
            storage: '512GB',
            price: 165000,
            description: 'Premium Android flagship with S Pen and incredible camera zoom',
            imageUrl: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
            featured: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '3',
            name: 'Google Pixel 8 Pro',
            storage: '128GB',
            price: 120000,
            description: 'Pure Android experience with exceptional AI photography',
            imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '4',
            name: 'OnePlus 12',
            storage: '256GB',
            price: 95000,
            description: 'Fast charging flagship with premium performance',
            imageUrl: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=500',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ];
        setPhones(demoPhones);
        setFilteredPhones(demoPhones);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Filter phones based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPhones(phones);
    } else {
      const filtered = phones.filter(phone =>
        phone.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        phone.storage.toLowerCase().includes(searchQuery.toLowerCase()) ||
        phone.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredPhones(filtered);
    }
  }, [searchQuery, phones]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const featuredPhones = phones.filter(phone => phone.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation onSearch={handleSearch} />

      {/* Hero Section */}
      <HeroCarousel featuredPhones={featuredPhones} />

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About High End Phones.ke
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We are Kenya's premier destination for high-end smartphones. Our commitment to quality,
              competitive pricing, and exceptional customer service makes us the trusted choice for
              premium mobile devices.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiPhone className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Premium Quality</h3>
              <p className="text-gray-600">Only the finest smartphones from trusted brands</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiMapPin className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Kenya-wide Delivery</h3>
              <p className="text-gray-600">Fast and secure delivery across Kenya</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <FiClock className="text-black text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
              <p className="text-gray-600">Round-the-clock customer support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Phones Section */}
      <section id="phones" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Premium Collection
            </h2>
            <p className="text-lg text-gray-600">
              Discover the latest high-end smartphones with cutting-edge technology
            </p>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
              <p className="mt-4 text-gray-600">Loading phones...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredPhones.map((phone) => (
                <PhoneCard key={phone.id} phone={phone} />
              ))}
            </div>
          )}

          {!loading && filteredPhones.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No phones found matching your search.</p>
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get in Touch
            </h2>
            <p className="text-lg text-gray-600">
              Ready to upgrade your phone? Contact us today!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <FiPhone className="text-yellow-600 text-xl" />
                  <span className="text-gray-700">+254 700 000 000</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiMail className="text-yellow-600 text-xl" />
                  <span className="text-gray-700"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiMapPin className="text-yellow-600 text-xl" />
                  <span className="text-gray-700">Nairobi, Kenya</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-2xl font-semibold mb-6">Business Hours</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-700">Monday - Friday</span>
                  <span className="text-gray-900 font-medium">9:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-700">Saturday</span>
                  <span className="text-gray-900 font-medium">10:00 AM - 4:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-700">Sunday</span>
                  <span className="text-gray-900 font-medium">Closed</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gradient mb-4">High End Phones.ke</h3>
            <p className="text-gray-400 mb-4">
              Your trusted partner for premium smartphones in Kenya
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 High End Phones.ke. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
