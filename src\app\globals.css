@import "tailwindcss";

:root {
  --primary-gold: #FFD700;
  --primary-black: #000000;
  --primary-white: #FFFFFF;
  --accent-gold: #F4C430;
  --text-gray: #333333;
  --bg-light: #FAFAFA;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

body {
  color: var(--text-gray);
  background: var(--bg-light);
}

a {
  color: inherit;
  text-decoration: none;
}

.gradient-gold {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-gold) 100%);
}

.gradient-black-gold {
  background: linear-gradient(135deg, var(--primary-black) 0%, var(--primary-gold) 100%);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-gold {
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
