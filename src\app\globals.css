@import "tailwindcss";

:root {
  --primary-gold: #FFD700;
  --primary-black: #000000;
  --primary-white: #FFFFFF;
  --accent-gold: #F4C430;
  --text-gray: #1a1a1a;
  --text-light: #6b7280;
  --bg-light: #fafafa;
  --bg-dark: #0f0f0f;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

body {
  color: var(--text-gray);
  background: var(--bg-light);
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Gradients */
.gradient-gold {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-gold) 100%);
}

.gradient-black-gold {
  background: linear-gradient(135deg, var(--primary-black) 0%, var(--primary-gold) 100%);
}

.gradient-soft {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glassmorphism */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neumorphism */
.neu-card {
  background: #f0f0f0;
  border-radius: 20px;
  box-shadow:
    20px 20px 60px #d1d1d1,
    -20px -20px 60px #ffffff;
}

.neu-button {
  background: #f0f0f0;
  border-radius: 15px;
  box-shadow:
    8px 8px 16px #d1d1d1,
    -8px -8px 16px #ffffff;
  transition: all 0.3s ease;
}

.neu-button:active {
  box-shadow:
    inset 8px 8px 16px #d1d1d1,
    inset -8px -8px 16px #ffffff;
}

/* Shadows */
.shadow-gold {
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
}

.shadow-soft {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.shadow-hover {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Animations */
.hover-lift {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
}

/* Pulse animation */
@keyframes pulse-gold {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }
}

.pulse-gold {
  animation: pulse-gold 2s infinite;
}

/* Skeleton loader */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Badges */
.badge-new {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-hot {
  background: linear-gradient(45deg, #ff9500, #ff6b00);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-sale {
  background: linear-gradient(45deg, #00d2ff, #3a7bd5);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gold);
}
