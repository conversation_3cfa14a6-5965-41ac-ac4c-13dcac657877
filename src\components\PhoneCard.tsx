'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { Phone } from '@/types/phone';
import { FiShoppingCart, FiHeart, FiEye, FiStar, FiZap } from 'react-icons/fi';

interface PhoneCardProps {
  phone: Phone;
  onBuy?: (phone: Phone) => void;
  isLoading?: boolean;
}

export default function PhoneCard({ phone, onBuy, isLoading }: PhoneCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleBuyClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onBuy) {
      onBuy(phone);
    } else {
      // Default WhatsApp integration
      const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;
      const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  const handleLikeClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
  };

  // Determine badge type
  const getBadge = () => {
    if (phone.featured) return { text: 'Featured', class: 'badge-hot' };
    if (phone.name.toLowerCase().includes('new')) return { text: 'New', class: 'badge-new' };
    if (phone.price < 100000) return { text: 'Deal', class: 'badge-sale' };
    return null;
  };

  const badge = getBadge();

  if (isLoading) {
    return (
      <div className="neu-card overflow-hidden">
        {/* Skeleton Image */}
        <div className="h-64 skeleton"></div>

        {/* Skeleton Content */}
        <div className="p-6 space-y-4">
          <div className="h-6 skeleton rounded"></div>
          <div className="flex justify-between items-center">
            <div className="h-4 w-16 skeleton rounded"></div>
            <div className="h-6 w-24 skeleton rounded"></div>
          </div>
          <div className="h-4 skeleton rounded"></div>
          <div className="h-10 skeleton rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <Link href={`/phone/${phone.id}`} className="block group">
      <div className="neu-card hover-lift overflow-hidden relative">
        {/* Phone Image */}
        <div className="relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
          {phone.imageUrl ? (
            <>
              {!imageLoaded && (
                <div className="absolute inset-0 skeleton"></div>
              )}
              <Image
                src={phone.imageUrl}
                alt={phone.name}
                fill
                className={`object-cover transition-all duration-500 group-hover:scale-110 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
              />
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-400 text-center">
                <FiShoppingCart size={48} />
                <p className="mt-2 font-medium">No Image</p>
              </div>
            </div>
          )}

          {/* Badges */}
          {badge && (
            <div className={`absolute top-3 left-3 ${badge.class} z-10`}>
              {badge.text}
            </div>
          )}

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
            <button
              onClick={handleLikeClick}
              className={`p-2 glass rounded-full transition-all duration-300 hover-glow ${
                isLiked ? 'text-red-500' : 'text-gray-600'
              }`}
            >
              <FiHeart size={18} fill={isLiked ? 'currentColor' : 'none'} />
            </button>
            <button className="p-2 glass rounded-full transition-all duration-300 hover-glow text-gray-600">
              <FiEye size={18} />
            </button>
          </div>

          {/* Quick Buy Overlay */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
            <button
              onClick={handleBuyClick}
              className="gradient-gold text-black font-semibold py-3 px-6 rounded-full hover-glow transition-all duration-300 transform scale-90 group-hover:scale-100 flex items-center space-x-2"
            >
              <FiShoppingCart size={18} />
              <span>Quick Buy</span>
            </button>
          </div>
        </div>

        {/* Phone Details */}
        <div className="p-6">
          {/* Rating */}
          <div className="flex items-center space-x-1 mb-2">
            {[...Array(5)].map((_, i) => (
              <FiStar
                key={i}
                size={14}
                className={i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'}
              />
            ))}
            <span className="text-sm text-gray-500 ml-2">(4.0)</span>
          </div>

          {/* Phone Name */}
          <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-yellow-600 transition-colors duration-200 line-clamp-1">
            {phone.name}
          </h3>

          {/* Storage and Price */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                {phone.storage}
              </span>
              {phone.name.toLowerCase().includes('5g') && (
                <span className="text-xs font-bold text-blue-600 bg-blue-100 px-2 py-1 rounded-full flex items-center space-x-1">
                  <FiZap size={12} />
                  <span>5G</span>
                </span>
              )}
            </div>
          </div>

          {/* Price */}
          <div className="mb-4">
            <div className="flex items-baseline space-x-2">
              <span className="text-2xl font-bold text-gradient">
                KSh {phone.price.toLocaleString()}
              </span>
              {phone.price > 150000 && (
                <span className="text-sm text-gray-500 line-through">
                  KSh {(phone.price * 1.2).toLocaleString()}
                </span>
              )}
            </div>
            {phone.price > 150000 && (
              <span className="text-sm text-green-600 font-medium">Save 20%</span>
            )}
          </div>

          {/* Description */}
          {phone.description && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
              {phone.description}
            </p>
          )}

          {/* Buy Button */}
          <button
            onClick={handleBuyClick}
            className="w-full neu-button text-gray-800 font-semibold py-3 px-4 transition-all duration-300 flex items-center justify-center space-x-2 hover:shadow-gold"
          >
            <FiShoppingCart size={18} />
            <span>Add to Cart</span>
          </button>

          {/* Features */}
          <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
            <span className="flex items-center space-x-1">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span>In Stock</span>
            </span>
            <span>Free Delivery</span>
            <span>1 Year Warranty</span>
          </div>
        </div>
      </div>
    </Link>
  );
}
