'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Phone } from '@/types/phone';
import { FiShoppingCart } from 'react-icons/fi';

interface PhoneCardProps {
  phone: Phone;
  onBuy?: (phone: Phone) => void;
}

export default function PhoneCard({ phone, onBuy }: PhoneCardProps) {
  const handleBuyClick = () => {
    if (onBuy) {
      onBuy(phone);
    } else {
      // Default WhatsApp integration
      const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;
      const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover-lift overflow-hidden">
      {/* Phone Image */}
      <Link href={`/phone/${phone.id}`}>
        <div className="relative h-64 bg-gray-100">
          {phone.imageUrl ? (
            <Image
              src={phone.imageUrl}
              alt={phone.name}
              fill
              className="object-cover hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-400 text-center">
                <FiShoppingCart size={48} />
                <p className="mt-2">No Image</p>
              </div>
            </div>
          )}
          {phone.featured && (
            <div className="absolute top-2 left-2 bg-yellow-500 text-black px-2 py-1 rounded-md text-xs font-semibold">
              Featured
            </div>
          )}
        </div>
      </Link>

      {/* Phone Details */}
      <div className="p-4">
        <Link href={`/phone/${phone.id}`}>
          <h3 className="text-lg font-semibold text-gray-900 hover:text-yellow-600 transition-colors duration-200 mb-2">
            {phone.name}
          </h3>
        </Link>
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
            {phone.storage}
          </span>
          <span className="text-xl font-bold text-yellow-600">
            KSh {phone.price.toLocaleString()}
          </span>
        </div>

        {phone.description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {phone.description}
          </p>
        )}

        {/* Buy Button */}
        <button
          onClick={handleBuyClick}
          className="w-full gradient-gold text-black font-semibold py-2 px-4 rounded-lg hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-2"
        >
          <FiShoppingCart size={18} />
          <span>Buy Now</span>
        </button>
      </div>
    </div>
  );
}
