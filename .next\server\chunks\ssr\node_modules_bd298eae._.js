module.exports = {

"[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */ /* eslint-disable node/no-deprecated-api */ var buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)");
var Buffer = buffer.Buffer;
// alternative to using Object.keys for old browsers
function copyProps(src, dst) {
    for(var key in src){
        dst[key] = src[key];
    }
}
if (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {
    module.exports = buffer;
} else {
    // Copy properties from require('buffer')
    copyProps(buffer, exports);
    exports.Buffer = SafeBuffer;
}
function SafeBuffer(arg, encodingOrOffset, length) {
    return Buffer(arg, encodingOrOffset, length);
}
SafeBuffer.prototype = Object.create(Buffer.prototype);
// Copy static methods from Buffer
copyProps(Buffer, SafeBuffer);
SafeBuffer.from = function(arg, encodingOrOffset, length) {
    if (typeof arg === 'number') {
        throw new TypeError('Argument must not be a number');
    }
    return Buffer(arg, encodingOrOffset, length);
};
SafeBuffer.alloc = function(size, fill, encoding) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    var buf = Buffer(size);
    if (fill !== undefined) {
        if (typeof encoding === 'string') {
            buf.fill(fill, encoding);
        } else {
            buf.fill(fill);
        }
    } else {
        buf.fill(0);
    }
    return buf;
};
SafeBuffer.allocUnsafe = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return Buffer(size);
};
SafeBuffer.allocUnsafeSlow = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return buffer.SlowBuffer(size);
};
}}),
"[project]/node_modules/websocket-driver/lib/websocket/streams.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**

Streams in a WebSocket connection
---------------------------------

We model a WebSocket as two duplex streams: one stream is for the wire protocol
over an I/O socket, and the other is for incoming/outgoing messages.


                        +----------+      +---------+      +----------+
    [1] write(chunk) -->| ~~~~~~~~ +----->| parse() +----->| ~~~~~~~~ +--> emit('data') [2]
                        |          |      +----+----+      |          |
                        |          |           |           |          |
                        |    IO    |           | [5]       | Messages |
                        |          |           V           |          |
                        |          |      +---------+      |          |
    [4] emit('data') <--+ ~~~~~~~~ |<-----+ frame() |<-----+ ~~~~~~~~ |<-- write(chunk) [3]
                        +----------+      +---------+      +----------+


Message transfer in each direction is simple: IO receives a byte stream [1] and
sends this stream for parsing. The parser will periodically emit a complete
message text on the Messages stream [2]. Similarly, when messages are written
to the Messages stream [3], they are framed using the WebSocket wire format and
emitted via IO [4].

There is a feedback loop via [5] since some input from [1] will be things like
ping, pong and close frames. In these cases the protocol responds by emitting
responses directly back to [4] rather than emitting messages via [2].

For the purposes of flow control, we consider the sources of each Readable
stream to be as follows:

* [2] receives input from [1]
* [4] receives input from [1] and [3]

The classes below express the relationships described above without prescribing
anything about how parse() and frame() work, other than assuming they emit
'data' events to the IO and Messages streams. They will work with any protocol
driver having these two methods.
**/ var Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").Stream, util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
var IO = function(driver) {
    this.readable = this.writable = true;
    this._paused = false;
    this._driver = driver;
};
util.inherits(IO, Stream);
// The IO pause() and resume() methods will be called when the socket we are
// piping to gets backed up and drains. Since IO output [4] comes from IO input
// [1] and Messages input [3], we need to tell both of those to return false
// from write() when this stream is paused.
IO.prototype.pause = function() {
    this._paused = true;
    this._driver.messages._paused = true;
};
IO.prototype.resume = function() {
    this._paused = false;
    this.emit('drain');
    var messages = this._driver.messages;
    messages._paused = false;
    messages.emit('drain');
};
// When we receive input from a socket, send it to the parser and tell the
// source whether to back off.
IO.prototype.write = function(chunk) {
    if (!this.writable) return false;
    this._driver.parse(chunk);
    return !this._paused;
};
// The IO end() method will be called when the socket piping into it emits
// 'close' or 'end', i.e. the socket is closed. In this situation the Messages
// stream will not emit any more data so we emit 'end'.
IO.prototype.end = function(chunk) {
    if (!this.writable) return;
    if (chunk !== undefined) this.write(chunk);
    this.writable = false;
    var messages = this._driver.messages;
    if (messages.readable) {
        messages.readable = messages.writable = false;
        messages.emit('end');
    }
};
IO.prototype.destroy = function() {
    this.end();
};
var Messages = function(driver) {
    this.readable = this.writable = true;
    this._paused = false;
    this._driver = driver;
};
util.inherits(Messages, Stream);
// The Messages pause() and resume() methods will be called when the app that's
// processing the messages gets backed up and drains. If we're emitting
// messages too fast we should tell the source to slow down. Message output [2]
// comes from IO input [1].
Messages.prototype.pause = function() {
    this._driver.io._paused = true;
};
Messages.prototype.resume = function() {
    this._driver.io._paused = false;
    this._driver.io.emit('drain');
};
// When we receive messages from the user, send them to the formatter and tell
// the source whether to back off.
Messages.prototype.write = function(message) {
    if (!this.writable) return false;
    if (typeof message === 'string') this._driver.text(message);
    else this._driver.binary(message);
    return !this._paused;
};
// The Messages end() method will be called when a stream piping into it emits
// 'end'. Many streams may be piped into the WebSocket and one of them ending
// does not mean the whole socket is done, so just process the input and move
// on leaving the socket open.
Messages.prototype.end = function(message) {
    if (message !== undefined) this.write(message);
};
Messages.prototype.destroy = function() {};
exports.IO = IO;
exports.Messages = Messages;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/headers.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Headers = function() {
    this.clear();
};
Headers.prototype.ALLOWED_DUPLICATES = [
    'set-cookie',
    'set-cookie2',
    'warning',
    'www-authenticate'
];
Headers.prototype.clear = function() {
    this._sent = {};
    this._lines = [];
};
Headers.prototype.set = function(name, value) {
    if (value === undefined) return;
    name = this._strip(name);
    value = this._strip(value);
    var key = name.toLowerCase();
    if (!this._sent.hasOwnProperty(key) || this.ALLOWED_DUPLICATES.indexOf(key) >= 0) {
        this._sent[key] = true;
        this._lines.push(name + ': ' + value + '\r\n');
    }
};
Headers.prototype.toString = function() {
    return this._lines.join('');
};
Headers.prototype._strip = function(string) {
    return string.toString().replace(/^ */, '').replace(/ *$/, '');
};
module.exports = Headers;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/stream_reader.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer;
var StreamReader = function() {
    this._queue = [];
    this._queueSize = 0;
    this._offset = 0;
};
StreamReader.prototype.put = function(buffer) {
    if (!buffer || buffer.length === 0) return;
    if (!Buffer.isBuffer(buffer)) buffer = Buffer.from(buffer);
    this._queue.push(buffer);
    this._queueSize += buffer.length;
};
StreamReader.prototype.read = function(length) {
    if (length > this._queueSize) return null;
    if (length === 0) return Buffer.alloc(0);
    this._queueSize -= length;
    var queue = this._queue, remain = length, first = queue[0], buffers, buffer;
    if (first.length >= length) {
        if (first.length === length) {
            return queue.shift();
        } else {
            buffer = first.slice(0, length);
            queue[0] = first.slice(length);
            return buffer;
        }
    }
    for(var i = 0, n = queue.length; i < n; i++){
        if (remain < queue[i].length) break;
        remain -= queue[i].length;
    }
    buffers = queue.splice(0, i);
    if (remain > 0 && queue.length > 0) {
        buffers.push(queue[0].slice(0, remain));
        queue[0] = queue[0].slice(remain);
    }
    return Buffer.concat(buffers, length);
};
StreamReader.prototype.eachByte = function(callback, context) {
    var buffer, n, index;
    while(this._queue.length > 0){
        buffer = this._queue[0];
        n = buffer.length;
        while(this._offset < n){
            index = this._offset;
            this._offset += 1;
            callback.call(context, buffer[index]);
        }
        this._offset = 0;
        this._queue.shift();
    }
};
module.exports = StreamReader;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, Emitter = __turbopack_context__.r("[externals]/events [external] (events, cjs)").EventEmitter, util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), streams = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/streams.js [app-ssr] (ecmascript)"), Headers = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/headers.js [app-ssr] (ecmascript)"), Reader = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/stream_reader.js [app-ssr] (ecmascript)");
var Base = function(request, url, options) {
    Emitter.call(this);
    Base.validateOptions(options || {}, [
        'maxLength',
        'masking',
        'requireMasking',
        'protocols'
    ]);
    this._request = request;
    this._reader = new Reader();
    this._options = options || {};
    this._maxLength = this._options.maxLength || this.MAX_LENGTH;
    this._headers = new Headers();
    this.__queue = [];
    this.readyState = 0;
    this.url = url;
    this.io = new streams.IO(this);
    this.messages = new streams.Messages(this);
    this._bindEventListeners();
};
util.inherits(Base, Emitter);
Base.isWebSocket = function(request) {
    var connection = request.headers.connection || '', upgrade = request.headers.upgrade || '';
    return request.method === 'GET' && connection.toLowerCase().split(/ *, */).indexOf('upgrade') >= 0 && upgrade.toLowerCase() === 'websocket';
};
Base.validateOptions = function(options, validKeys) {
    for(var key in options){
        if (validKeys.indexOf(key) < 0) throw new Error('Unrecognized option: ' + key);
    }
};
var instance = {
    // This is 64MB, small enough for an average VPS to handle without
    // crashing from process out of memory
    MAX_LENGTH: 0x3ffffff,
    STATES: [
        'connecting',
        'open',
        'closing',
        'closed'
    ],
    _bindEventListeners: function() {
        var self = this;
        // Protocol errors are informational and do not have to be handled
        this.messages.on('error', function() {});
        this.on('message', function(event) {
            var messages = self.messages;
            if (messages.readable) messages.emit('data', event.data);
        });
        this.on('error', function(error) {
            var messages = self.messages;
            if (messages.readable) messages.emit('error', error);
        });
        this.on('close', function() {
            var messages = self.messages;
            if (!messages.readable) return;
            messages.readable = messages.writable = false;
            messages.emit('end');
        });
    },
    getState: function() {
        return this.STATES[this.readyState] || null;
    },
    addExtension: function(extension) {
        return false;
    },
    setHeader: function(name, value) {
        if (this.readyState > 0) return false;
        this._headers.set(name, value);
        return true;
    },
    start: function() {
        if (this.readyState !== 0) return false;
        if (!Base.isWebSocket(this._request)) return this._failHandshake(new Error('Not a WebSocket request'));
        var response;
        try {
            response = this._handshakeResponse();
        } catch (error) {
            return this._failHandshake(error);
        }
        this._write(response);
        if (this._stage !== -1) this._open();
        return true;
    },
    _failHandshake: function(error) {
        var headers = new Headers();
        headers.set('Content-Type', 'text/plain');
        headers.set('Content-Length', Buffer.byteLength(error.message, 'utf8'));
        headers = [
            'HTTP/1.1 400 Bad Request',
            headers.toString(),
            error.message
        ];
        this._write(Buffer.from(headers.join('\r\n'), 'utf8'));
        this._fail('protocol_error', error.message);
        return false;
    },
    text: function(message) {
        return this.frame(message);
    },
    binary: function(message) {
        return false;
    },
    ping: function() {
        return false;
    },
    pong: function() {
        return false;
    },
    close: function(reason, code) {
        if (this.readyState !== 1) return false;
        this.readyState = 3;
        this.emit('close', new Base.CloseEvent(null, null));
        return true;
    },
    _open: function() {
        this.readyState = 1;
        this.__queue.forEach(function(args) {
            this.frame.apply(this, args);
        }, this);
        this.__queue = [];
        this.emit('open', new Base.OpenEvent());
    },
    _queue: function(message) {
        this.__queue.push(message);
        return true;
    },
    _write: function(chunk) {
        var io = this.io;
        if (io.readable) io.emit('data', chunk);
    },
    _fail: function(type, message) {
        this.readyState = 2;
        this.emit('error', new Error(message));
        this.close();
    }
};
for(var key in instance)Base.prototype[key] = instance[key];
Base.ConnectEvent = function() {};
Base.OpenEvent = function() {};
Base.CloseEvent = function(code, reason) {
    this.code = code;
    this.reason = reason;
};
Base.MessageEvent = function(data) {
    this.data = data;
};
Base.PingEvent = function(data) {
    this.data = data;
};
Base.PongEvent = function(data) {
    this.data = data;
};
module.exports = Base;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/http_parser.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var NodeHTTPParser = __turbopack_context__.r("[project]/node_modules/http-parser-js/http-parser.js [app-ssr] (ecmascript)").HTTPParser, Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer;
var TYPES = {
    request: NodeHTTPParser.REQUEST || 'request',
    response: NodeHTTPParser.RESPONSE || 'response'
};
var HttpParser = function(type) {
    this._type = type;
    this._parser = new NodeHTTPParser(TYPES[type]);
    this._complete = false;
    this.headers = {};
    var current = null, self = this;
    this._parser.onHeaderField = function(b, start, length) {
        current = b.toString('utf8', start, start + length).toLowerCase();
    };
    this._parser.onHeaderValue = function(b, start, length) {
        var value = b.toString('utf8', start, start + length);
        if (self.headers.hasOwnProperty(current)) self.headers[current] += ', ' + value;
        else self.headers[current] = value;
    };
    this._parser.onHeadersComplete = this._parser[NodeHTTPParser.kOnHeadersComplete] = function(majorVersion, minorVersion, headers, method, pathname, statusCode) {
        var info = arguments[0];
        if (typeof info === 'object') {
            method = info.method;
            pathname = info.url;
            statusCode = info.statusCode;
            headers = info.headers;
        }
        self.method = typeof method === 'number' ? HttpParser.METHODS[method] : method;
        self.statusCode = statusCode;
        self.url = pathname;
        if (!headers) return;
        for(var i = 0, n = headers.length, key, value; i < n; i += 2){
            key = headers[i].toLowerCase();
            value = headers[i + 1];
            if (self.headers.hasOwnProperty(key)) self.headers[key] += ', ' + value;
            else self.headers[key] = value;
        }
        self._complete = true;
    };
};
HttpParser.METHODS = {
    0: 'DELETE',
    1: 'GET',
    2: 'HEAD',
    3: 'POST',
    4: 'PUT',
    5: 'CONNECT',
    6: 'OPTIONS',
    7: 'TRACE',
    8: 'COPY',
    9: 'LOCK',
    10: 'MKCOL',
    11: 'MOVE',
    12: 'PROPFIND',
    13: 'PROPPATCH',
    14: 'SEARCH',
    15: 'UNLOCK',
    16: 'BIND',
    17: 'REBIND',
    18: 'UNBIND',
    19: 'ACL',
    20: 'REPORT',
    21: 'MKACTIVITY',
    22: 'CHECKOUT',
    23: 'MERGE',
    24: 'M-SEARCH',
    25: 'NOTIFY',
    26: 'SUBSCRIBE',
    27: 'UNSUBSCRIBE',
    28: 'PATCH',
    29: 'PURGE',
    30: 'MKCALENDAR',
    31: 'LINK',
    32: 'UNLINK'
};
var VERSION = process.version ? process.version.match(/[0-9]+/g).map(function(n) {
    return parseInt(n, 10);
}) : [];
if (VERSION[0] === 0 && VERSION[1] === 12) {
    HttpParser.METHODS[16] = 'REPORT';
    HttpParser.METHODS[17] = 'MKACTIVITY';
    HttpParser.METHODS[18] = 'CHECKOUT';
    HttpParser.METHODS[19] = 'MERGE';
    HttpParser.METHODS[20] = 'M-SEARCH';
    HttpParser.METHODS[21] = 'NOTIFY';
    HttpParser.METHODS[22] = 'SUBSCRIBE';
    HttpParser.METHODS[23] = 'UNSUBSCRIBE';
    HttpParser.METHODS[24] = 'PATCH';
    HttpParser.METHODS[25] = 'PURGE';
}
HttpParser.prototype.isComplete = function() {
    return this._complete;
};
HttpParser.prototype.parse = function(chunk) {
    var consumed = this._parser.execute(chunk, 0, chunk.length);
    if (typeof consumed !== 'number') {
        this.error = consumed;
        this._complete = true;
        return;
    }
    if (this._complete) this.body = consumed < chunk.length ? chunk.slice(consumed) : Buffer.alloc(0);
};
module.exports = HttpParser;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Frame = function() {};
var instance = {
    final: false,
    rsv1: false,
    rsv2: false,
    rsv3: false,
    opcode: null,
    masked: false,
    maskingKey: null,
    lengthBytes: 1,
    length: 0,
    payload: null
};
for(var key in instance)Frame.prototype[key] = instance[key];
module.exports = Frame;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/hybi/message.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer;
var Message = function() {
    this.rsv1 = false;
    this.rsv2 = false;
    this.rsv3 = false;
    this.opcode = null;
    this.length = 0;
    this._chunks = [];
};
var instance = {
    read: function() {
        return this.data = this.data || Buffer.concat(this._chunks, this.length);
    },
    pushFrame: function(frame) {
        this.rsv1 = this.rsv1 || frame.rsv1;
        this.rsv2 = this.rsv2 || frame.rsv2;
        this.rsv3 = this.rsv3 || frame.rsv3;
        if (this.opcode === null) this.opcode = frame.opcode;
        this._chunks.push(frame.payload);
        this.length += frame.length;
    }
};
for(var key in instance)Message.prototype[key] = instance[key];
module.exports = Message;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/hybi.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, crypto = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)"), util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), Extensions = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/websocket_extensions.js [app-ssr] (ecmascript)"), Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Frame = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js [app-ssr] (ecmascript)"), Message = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/hybi/message.js [app-ssr] (ecmascript)");
var Hybi = function(request, url, options) {
    Base.apply(this, arguments);
    this._extensions = new Extensions();
    this._stage = 0;
    this._masking = this._options.masking;
    this._protocols = this._options.protocols || [];
    this._requireMasking = this._options.requireMasking;
    this._pingCallbacks = {};
    if (typeof this._protocols === 'string') this._protocols = this._protocols.split(/ *, */);
    if (!this._request) return;
    var protos = this._request.headers['sec-websocket-protocol'], supported = this._protocols;
    if (protos !== undefined) {
        if (typeof protos === 'string') protos = protos.split(/ *, */);
        this.protocol = protos.filter(function(p) {
            return supported.indexOf(p) >= 0;
        })[0];
    }
    this.version = 'hybi-' + Hybi.VERSION;
};
util.inherits(Hybi, Base);
Hybi.VERSION = '13';
Hybi.mask = function(payload, mask, offset) {
    if (!mask || mask.length === 0) return payload;
    offset = offset || 0;
    for(var i = 0, n = payload.length - offset; i < n; i++){
        payload[offset + i] = payload[offset + i] ^ mask[i % 4];
    }
    return payload;
};
Hybi.generateAccept = function(key) {
    var sha1 = crypto.createHash('sha1');
    sha1.update(key + Hybi.GUID);
    return sha1.digest('base64');
};
Hybi.GUID = '258EAFA5-E914-47DA-95CA-C5AB0DC85B11';
var instance = {
    FIN: 0x80,
    MASK: 0x80,
    RSV1: 0x40,
    RSV2: 0x20,
    RSV3: 0x10,
    OPCODE: 0x0F,
    LENGTH: 0x7F,
    OPCODES: {
        continuation: 0,
        text: 1,
        binary: 2,
        close: 8,
        ping: 9,
        pong: 10
    },
    OPCODE_CODES: [
        0,
        1,
        2,
        8,
        9,
        10
    ],
    MESSAGE_OPCODES: [
        0,
        1,
        2
    ],
    OPENING_OPCODES: [
        1,
        2
    ],
    ERRORS: {
        normal_closure: 1000,
        going_away: 1001,
        protocol_error: 1002,
        unacceptable: 1003,
        encoding_error: 1007,
        policy_violation: 1008,
        too_large: 1009,
        extension_error: 1010,
        unexpected_condition: 1011
    },
    ERROR_CODES: [
        1000,
        1001,
        1002,
        1003,
        1007,
        1008,
        1009,
        1010,
        1011
    ],
    DEFAULT_ERROR_CODE: 1000,
    MIN_RESERVED_ERROR: 3000,
    MAX_RESERVED_ERROR: 4999,
    // http://www.w3.org/International/questions/qa-forms-utf-8.en.php
    UTF8_MATCH: /^([\x00-\x7F]|[\xC2-\xDF][\x80-\xBF]|\xE0[\xA0-\xBF][\x80-\xBF]|[\xE1-\xEC\xEE\xEF][\x80-\xBF]{2}|\xED[\x80-\x9F][\x80-\xBF]|\xF0[\x90-\xBF][\x80-\xBF]{2}|[\xF1-\xF3][\x80-\xBF]{3}|\xF4[\x80-\x8F][\x80-\xBF]{2})*$/,
    addExtension: function(extension) {
        this._extensions.add(extension);
        return true;
    },
    parse: function(chunk) {
        this._reader.put(chunk);
        var buffer = true;
        while(buffer){
            switch(this._stage){
                case 0:
                    buffer = this._reader.read(1);
                    if (buffer) this._parseOpcode(buffer[0]);
                    break;
                case 1:
                    buffer = this._reader.read(1);
                    if (buffer) this._parseLength(buffer[0]);
                    break;
                case 2:
                    buffer = this._reader.read(this._frame.lengthBytes);
                    if (buffer) this._parseExtendedLength(buffer);
                    break;
                case 3:
                    buffer = this._reader.read(4);
                    if (buffer) {
                        this._stage = 4;
                        this._frame.maskingKey = buffer;
                    }
                    break;
                case 4:
                    buffer = this._reader.read(this._frame.length);
                    if (buffer) {
                        this._stage = 0;
                        this._emitFrame(buffer);
                    }
                    break;
                default:
                    buffer = null;
            }
        }
    },
    text: function(message) {
        if (this.readyState > 1) return false;
        return this.frame(message, 'text');
    },
    binary: function(message) {
        if (this.readyState > 1) return false;
        return this.frame(message, 'binary');
    },
    ping: function(message, callback) {
        if (this.readyState > 1) return false;
        message = message || '';
        if (callback) this._pingCallbacks[message] = callback;
        return this.frame(message, 'ping');
    },
    pong: function(message) {
        if (this.readyState > 1) return false;
        message = message || '';
        return this.frame(message, 'pong');
    },
    close: function(reason, code) {
        reason = reason || '';
        code = code || this.ERRORS.normal_closure;
        if (this.readyState <= 0) {
            this.readyState = 3;
            this.emit('close', new Base.CloseEvent(code, reason));
            return true;
        } else if (this.readyState === 1) {
            this.readyState = 2;
            this._extensions.close(function() {
                this.frame(reason, 'close', code);
            }, this);
            return true;
        } else {
            return false;
        }
    },
    frame: function(buffer, type, code) {
        if (this.readyState <= 0) return this._queue([
            buffer,
            type,
            code
        ]);
        if (this.readyState > 2) return false;
        if (buffer instanceof Array) buffer = Buffer.from(buffer);
        if (typeof buffer === 'number') buffer = buffer.toString();
        var message = new Message(), isText = typeof buffer === 'string', payload, copy;
        message.rsv1 = message.rsv2 = message.rsv3 = false;
        message.opcode = this.OPCODES[type || (isText ? 'text' : 'binary')];
        payload = isText ? Buffer.from(buffer, 'utf8') : buffer;
        if (code) {
            copy = payload;
            payload = Buffer.allocUnsafe(2 + copy.length);
            payload.writeUInt16BE(code, 0);
            copy.copy(payload, 2);
        }
        message.data = payload;
        var onMessageReady = function(message) {
            var frame = new Frame();
            frame.final = true;
            frame.rsv1 = message.rsv1;
            frame.rsv2 = message.rsv2;
            frame.rsv3 = message.rsv3;
            frame.opcode = message.opcode;
            frame.masked = !!this._masking;
            frame.length = message.data.length;
            frame.payload = message.data;
            if (frame.masked) frame.maskingKey = crypto.randomBytes(4);
            this._sendFrame(frame);
        };
        if (this.MESSAGE_OPCODES.indexOf(message.opcode) >= 0) this._extensions.processOutgoingMessage(message, function(error, message) {
            if (error) return this._fail('extension_error', error.message);
            onMessageReady.call(this, message);
        }, this);
        else onMessageReady.call(this, message);
        return true;
    },
    _sendFrame: function(frame) {
        var length = frame.length, header = length <= 125 ? 2 : length <= 65535 ? 4 : 10, offset = header + (frame.masked ? 4 : 0), buffer = Buffer.allocUnsafe(offset + length), masked = frame.masked ? this.MASK : 0;
        buffer[0] = (frame.final ? this.FIN : 0) | (frame.rsv1 ? this.RSV1 : 0) | (frame.rsv2 ? this.RSV2 : 0) | (frame.rsv3 ? this.RSV3 : 0) | frame.opcode;
        if (length <= 125) {
            buffer[1] = masked | length;
        } else if (length <= 65535) {
            buffer[1] = masked | 126;
            buffer.writeUInt16BE(length, 2);
        } else {
            buffer[1] = masked | 127;
            buffer.writeUInt32BE(Math.floor(length / 0x100000000), 2);
            buffer.writeUInt32BE(length % 0x100000000, 6);
        }
        frame.payload.copy(buffer, offset);
        if (frame.masked) {
            frame.maskingKey.copy(buffer, header);
            Hybi.mask(buffer, frame.maskingKey, offset);
        }
        this._write(buffer);
    },
    _handshakeResponse: function() {
        var secKey = this._request.headers['sec-websocket-key'], version = this._request.headers['sec-websocket-version'];
        if (version !== Hybi.VERSION) throw new Error('Unsupported WebSocket version: ' + version);
        if (typeof secKey !== 'string') throw new Error('Missing handshake request header: Sec-WebSocket-Key');
        this._headers.set('Upgrade', 'websocket');
        this._headers.set('Connection', 'Upgrade');
        this._headers.set('Sec-WebSocket-Accept', Hybi.generateAccept(secKey));
        if (this.protocol) this._headers.set('Sec-WebSocket-Protocol', this.protocol);
        var extensions = this._extensions.generateResponse(this._request.headers['sec-websocket-extensions']);
        if (extensions) this._headers.set('Sec-WebSocket-Extensions', extensions);
        var start = 'HTTP/1.1 101 Switching Protocols', headers = [
            start,
            this._headers.toString(),
            ''
        ];
        return Buffer.from(headers.join('\r\n'), 'utf8');
    },
    _shutdown: function(code, reason, error) {
        delete this._frame;
        delete this._message;
        this._stage = 5;
        var sendCloseFrame = this.readyState === 1;
        this.readyState = 2;
        this._extensions.close(function() {
            if (sendCloseFrame) this.frame(reason, 'close', code);
            this.readyState = 3;
            if (error) this.emit('error', new Error(reason));
            this.emit('close', new Base.CloseEvent(code, reason));
        }, this);
    },
    _fail: function(type, message) {
        if (this.readyState > 1) return;
        this._shutdown(this.ERRORS[type], message, true);
    },
    _parseOpcode: function(octet) {
        var rsvs = [
            this.RSV1,
            this.RSV2,
            this.RSV3
        ].map(function(rsv) {
            return (octet & rsv) === rsv;
        });
        var frame = this._frame = new Frame();
        frame.final = (octet & this.FIN) === this.FIN;
        frame.rsv1 = rsvs[0];
        frame.rsv2 = rsvs[1];
        frame.rsv3 = rsvs[2];
        frame.opcode = octet & this.OPCODE;
        this._stage = 1;
        if (!this._extensions.validFrameRsv(frame)) return this._fail('protocol_error', 'One or more reserved bits are on: reserved1 = ' + (frame.rsv1 ? 1 : 0) + ', reserved2 = ' + (frame.rsv2 ? 1 : 0) + ', reserved3 = ' + (frame.rsv3 ? 1 : 0));
        if (this.OPCODE_CODES.indexOf(frame.opcode) < 0) return this._fail('protocol_error', 'Unrecognized frame opcode: ' + frame.opcode);
        if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && !frame.final) return this._fail('protocol_error', 'Received fragmented control frame: opcode = ' + frame.opcode);
        if (this._message && this.OPENING_OPCODES.indexOf(frame.opcode) >= 0) return this._fail('protocol_error', 'Received new data frame but previous continuous frame is unfinished');
    },
    _parseLength: function(octet) {
        var frame = this._frame;
        frame.masked = (octet & this.MASK) === this.MASK;
        frame.length = octet & this.LENGTH;
        if (frame.length >= 0 && frame.length <= 125) {
            this._stage = frame.masked ? 3 : 4;
            if (!this._checkFrameLength()) return;
        } else {
            this._stage = 2;
            frame.lengthBytes = frame.length === 126 ? 2 : 8;
        }
        if (this._requireMasking && !frame.masked) return this._fail('unacceptable', 'Received unmasked frame but masking is required');
    },
    _parseExtendedLength: function(buffer) {
        var frame = this._frame;
        frame.length = this._readUInt(buffer);
        this._stage = frame.masked ? 3 : 4;
        if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && frame.length > 125) return this._fail('protocol_error', 'Received control frame having too long payload: ' + frame.length);
        if (!this._checkFrameLength()) return;
    },
    _checkFrameLength: function() {
        var length = this._message ? this._message.length : 0;
        if (length + this._frame.length > this._maxLength) {
            this._fail('too_large', 'WebSocket frame length too large');
            return false;
        } else {
            return true;
        }
    },
    _emitFrame: function(buffer) {
        var frame = this._frame, payload = frame.payload = Hybi.mask(buffer, frame.maskingKey), opcode = frame.opcode, message, code, reason, callbacks, callback;
        delete this._frame;
        if (opcode === this.OPCODES.continuation) {
            if (!this._message) return this._fail('protocol_error', 'Received unexpected continuation frame');
            this._message.pushFrame(frame);
        }
        if (opcode === this.OPCODES.text || opcode === this.OPCODES.binary) {
            this._message = new Message();
            this._message.pushFrame(frame);
        }
        if (frame.final && this.MESSAGE_OPCODES.indexOf(opcode) >= 0) return this._emitMessage(this._message);
        if (opcode === this.OPCODES.close) {
            code = payload.length >= 2 ? payload.readUInt16BE(0) : null;
            reason = payload.length > 2 ? this._encode(payload.slice(2)) : null;
            if (!(payload.length === 0) && !(code !== null && code >= this.MIN_RESERVED_ERROR && code <= this.MAX_RESERVED_ERROR) && this.ERROR_CODES.indexOf(code) < 0) code = this.ERRORS.protocol_error;
            if (payload.length > 125 || payload.length > 2 && !reason) code = this.ERRORS.protocol_error;
            this._shutdown(code || this.DEFAULT_ERROR_CODE, reason || '');
        }
        if (opcode === this.OPCODES.ping) {
            this.frame(payload, 'pong');
            this.emit('ping', new Base.PingEvent(payload.toString()));
        }
        if (opcode === this.OPCODES.pong) {
            callbacks = this._pingCallbacks;
            message = this._encode(payload);
            callback = callbacks[message];
            delete callbacks[message];
            if (callback) callback();
            this.emit('pong', new Base.PongEvent(payload.toString()));
        }
    },
    _emitMessage: function(message) {
        var message = this._message;
        message.read();
        delete this._message;
        this._extensions.processIncomingMessage(message, function(error, message) {
            if (error) return this._fail('extension_error', error.message);
            var payload = message.data;
            if (message.opcode === this.OPCODES.text) payload = this._encode(payload);
            if (payload === null) return this._fail('encoding_error', 'Could not decode a text frame as UTF-8');
            else this.emit('message', new Base.MessageEvent(payload));
        }, this);
    },
    _encode: function(buffer) {
        try {
            var string = buffer.toString('binary', 0, buffer.length);
            if (!this.UTF8_MATCH.test(string)) return null;
        } catch (e) {}
        return buffer.toString('utf8', 0, buffer.length);
    },
    _readUInt: function(buffer) {
        if (buffer.length === 2) return buffer.readUInt16BE(0);
        return buffer.readUInt32BE(0) * 0x100000000 + buffer.readUInt32BE(4);
    }
};
for(var key in instance)Hybi.prototype[key] = instance[key];
module.exports = Hybi;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/proxy.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").Stream, url = __turbopack_context__.r("[externals]/url [external] (url, cjs)"), util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Headers = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/headers.js [app-ssr] (ecmascript)"), HttpParser = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/http_parser.js [app-ssr] (ecmascript)");
var PORTS = {
    'ws:': 80,
    'wss:': 443
};
var Proxy = function(client, origin, options) {
    this._client = client;
    this._http = new HttpParser('response');
    this._origin = typeof client.url === 'object' ? client.url : url.parse(client.url);
    this._url = typeof origin === 'object' ? origin : url.parse(origin);
    this._options = options || {};
    this._state = 0;
    this.readable = this.writable = true;
    this._paused = false;
    this._headers = new Headers();
    this._headers.set('Host', this._origin.host);
    this._headers.set('Connection', 'keep-alive');
    this._headers.set('Proxy-Connection', 'keep-alive');
    var auth = this._url.auth && Buffer.from(this._url.auth, 'utf8').toString('base64');
    if (auth) this._headers.set('Proxy-Authorization', 'Basic ' + auth);
};
util.inherits(Proxy, Stream);
var instance = {
    setHeader: function(name, value) {
        if (this._state !== 0) return false;
        this._headers.set(name, value);
        return true;
    },
    start: function() {
        if (this._state !== 0) return false;
        this._state = 1;
        var origin = this._origin, port = origin.port || PORTS[origin.protocol], start = 'CONNECT ' + origin.hostname + ':' + port + ' HTTP/1.1';
        var headers = [
            start,
            this._headers.toString(),
            ''
        ];
        this.emit('data', Buffer.from(headers.join('\r\n'), 'utf8'));
        return true;
    },
    pause: function() {
        this._paused = true;
    },
    resume: function() {
        this._paused = false;
        this.emit('drain');
    },
    write: function(chunk) {
        if (!this.writable) return false;
        this._http.parse(chunk);
        if (!this._http.isComplete()) return !this._paused;
        this.statusCode = this._http.statusCode;
        this.headers = this._http.headers;
        if (this.statusCode === 200) {
            this.emit('connect', new Base.ConnectEvent());
        } else {
            var message = "Can't establish a connection to the server at " + this._origin.href;
            this.emit('error', new Error(message));
        }
        this.end();
        return !this._paused;
    },
    end: function(chunk) {
        if (!this.writable) return;
        if (chunk !== undefined) this.write(chunk);
        this.readable = this.writable = false;
        this.emit('close');
        this.emit('end');
    },
    destroy: function() {
        this.end();
    }
};
for(var key in instance)Proxy.prototype[key] = instance[key];
module.exports = Proxy;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/client.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, crypto = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)"), url = __turbopack_context__.r("[externals]/url [external] (url, cjs)"), util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), HttpParser = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/http_parser.js [app-ssr] (ecmascript)"), Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Hybi = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/hybi.js [app-ssr] (ecmascript)"), Proxy = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/proxy.js [app-ssr] (ecmascript)");
var Client = function(_url, options) {
    this.version = 'hybi-' + Hybi.VERSION;
    Hybi.call(this, null, _url, options);
    this.readyState = -1;
    this._key = Client.generateKey();
    this._accept = Hybi.generateAccept(this._key);
    this._http = new HttpParser('response');
    var uri = url.parse(this.url), auth = uri.auth && Buffer.from(uri.auth, 'utf8').toString('base64');
    if (this.VALID_PROTOCOLS.indexOf(uri.protocol) < 0) throw new Error(this.url + ' is not a valid WebSocket URL');
    this._pathname = (uri.pathname || '/') + (uri.search || '');
    this._headers.set('Host', uri.host);
    this._headers.set('Upgrade', 'websocket');
    this._headers.set('Connection', 'Upgrade');
    this._headers.set('Sec-WebSocket-Key', this._key);
    this._headers.set('Sec-WebSocket-Version', Hybi.VERSION);
    if (this._protocols.length > 0) this._headers.set('Sec-WebSocket-Protocol', this._protocols.join(', '));
    if (auth) this._headers.set('Authorization', 'Basic ' + auth);
};
util.inherits(Client, Hybi);
Client.generateKey = function() {
    return crypto.randomBytes(16).toString('base64');
};
var instance = {
    VALID_PROTOCOLS: [
        'ws:',
        'wss:'
    ],
    proxy: function(origin, options) {
        return new Proxy(this, origin, options);
    },
    start: function() {
        if (this.readyState !== -1) return false;
        this._write(this._handshakeRequest());
        this.readyState = 0;
        return true;
    },
    parse: function(chunk) {
        if (this.readyState === 3) return;
        if (this.readyState > 0) return Hybi.prototype.parse.call(this, chunk);
        this._http.parse(chunk);
        if (!this._http.isComplete()) return;
        this._validateHandshake();
        if (this.readyState === 3) return;
        this._open();
        this.parse(this._http.body);
    },
    _handshakeRequest: function() {
        var extensions = this._extensions.generateOffer();
        if (extensions) this._headers.set('Sec-WebSocket-Extensions', extensions);
        var start = 'GET ' + this._pathname + ' HTTP/1.1', headers = [
            start,
            this._headers.toString(),
            ''
        ];
        return Buffer.from(headers.join('\r\n'), 'utf8');
    },
    _failHandshake: function(message) {
        message = 'Error during WebSocket handshake: ' + message;
        this.readyState = 3;
        this.emit('error', new Error(message));
        this.emit('close', new Base.CloseEvent(this.ERRORS.protocol_error, message));
    },
    _validateHandshake: function() {
        this.statusCode = this._http.statusCode;
        this.headers = this._http.headers;
        if (this._http.error) return this._failHandshake(this._http.error.message);
        if (this._http.statusCode !== 101) return this._failHandshake('Unexpected response code: ' + this._http.statusCode);
        var headers = this._http.headers, upgrade = headers['upgrade'] || '', connection = headers['connection'] || '', accept = headers['sec-websocket-accept'] || '', protocol = headers['sec-websocket-protocol'] || '';
        if (upgrade === '') return this._failHandshake("'Upgrade' header is missing");
        if (upgrade.toLowerCase() !== 'websocket') return this._failHandshake("'Upgrade' header value is not 'WebSocket'");
        if (connection === '') return this._failHandshake("'Connection' header is missing");
        if (connection.toLowerCase() !== 'upgrade') return this._failHandshake("'Connection' header value is not 'Upgrade'");
        if (accept !== this._accept) return this._failHandshake('Sec-WebSocket-Accept mismatch');
        this.protocol = null;
        if (protocol !== '') {
            if (this._protocols.indexOf(protocol) < 0) return this._failHandshake('Sec-WebSocket-Protocol mismatch');
            else this.protocol = protocol;
        }
        try {
            this._extensions.activate(this.headers['sec-websocket-extensions']);
        } catch (e) {
            return this._failHandshake(e.message);
        }
    }
};
for(var key in instance)Client.prototype[key] = instance[key];
module.exports = Client;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/draft75.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
var Draft75 = function(request, url, options) {
    Base.apply(this, arguments);
    this._stage = 0;
    this.version = 'hixie-75';
    this._headers.set('Upgrade', 'WebSocket');
    this._headers.set('Connection', 'Upgrade');
    this._headers.set('WebSocket-Origin', this._request.headers.origin);
    this._headers.set('WebSocket-Location', this.url);
};
util.inherits(Draft75, Base);
var instance = {
    close: function() {
        if (this.readyState === 3) return false;
        this.readyState = 3;
        this.emit('close', new Base.CloseEvent(null, null));
        return true;
    },
    parse: function(chunk) {
        if (this.readyState > 1) return;
        this._reader.put(chunk);
        this._reader.eachByte(function(octet) {
            var message;
            switch(this._stage){
                case -1:
                    this._body.push(octet);
                    this._sendHandshakeBody();
                    break;
                case 0:
                    this._parseLeadingByte(octet);
                    break;
                case 1:
                    this._length = (octet & 0x7F) + 128 * this._length;
                    if (this._closing && this._length === 0) {
                        return this.close();
                    } else if ((octet & 0x80) !== 0x80) {
                        if (this._length === 0) {
                            this._stage = 0;
                        } else {
                            this._skipped = 0;
                            this._stage = 2;
                        }
                    }
                    break;
                case 2:
                    if (octet === 0xFF) {
                        this._stage = 0;
                        message = Buffer.from(this._buffer).toString('utf8', 0, this._buffer.length);
                        this.emit('message', new Base.MessageEvent(message));
                    } else {
                        if (this._length) {
                            this._skipped += 1;
                            if (this._skipped === this._length) this._stage = 0;
                        } else {
                            this._buffer.push(octet);
                            if (this._buffer.length > this._maxLength) return this.close();
                        }
                    }
                    break;
            }
        }, this);
    },
    frame: function(buffer) {
        if (this.readyState === 0) return this._queue([
            buffer
        ]);
        if (this.readyState > 1) return false;
        if (typeof buffer !== 'string') buffer = buffer.toString();
        var length = Buffer.byteLength(buffer), frame = Buffer.allocUnsafe(length + 2);
        frame[0] = 0x00;
        frame.write(buffer, 1);
        frame[frame.length - 1] = 0xFF;
        this._write(frame);
        return true;
    },
    _handshakeResponse: function() {
        var start = 'HTTP/1.1 101 Web Socket Protocol Handshake', headers = [
            start,
            this._headers.toString(),
            ''
        ];
        return Buffer.from(headers.join('\r\n'), 'utf8');
    },
    _parseLeadingByte: function(octet) {
        if ((octet & 0x80) === 0x80) {
            this._length = 0;
            this._stage = 1;
        } else {
            delete this._length;
            delete this._skipped;
            this._buffer = [];
            this._stage = 2;
        }
    }
};
for(var key in instance)Draft75.prototype[key] = instance[key];
module.exports = Draft75;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/draft76.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-ssr] (ecmascript)").Buffer, Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Draft75 = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/draft75.js [app-ssr] (ecmascript)"), crypto = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)"), util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
var numberFromKey = function(key) {
    return parseInt((key.match(/[0-9]/g) || []).join(''), 10);
};
var spacesInKey = function(key) {
    return (key.match(/ /g) || []).length;
};
var Draft76 = function(request, url, options) {
    Draft75.apply(this, arguments);
    this._stage = -1;
    this._body = [];
    this.version = 'hixie-76';
    this._headers.clear();
    this._headers.set('Upgrade', 'WebSocket');
    this._headers.set('Connection', 'Upgrade');
    this._headers.set('Sec-WebSocket-Origin', this._request.headers.origin);
    this._headers.set('Sec-WebSocket-Location', this.url);
};
util.inherits(Draft76, Draft75);
var instance = {
    BODY_SIZE: 8,
    start: function() {
        if (!Draft75.prototype.start.call(this)) return false;
        this._started = true;
        this._sendHandshakeBody();
        return true;
    },
    close: function() {
        if (this.readyState === 3) return false;
        if (this.readyState === 1) this._write(Buffer.from([
            0xFF,
            0x00
        ]));
        this.readyState = 3;
        this.emit('close', new Base.CloseEvent(null, null));
        return true;
    },
    _handshakeResponse: function() {
        var headers = this._request.headers, key1 = headers['sec-websocket-key1'], key2 = headers['sec-websocket-key2'];
        if (!key1) throw new Error('Missing required header: Sec-WebSocket-Key1');
        if (!key2) throw new Error('Missing required header: Sec-WebSocket-Key2');
        var number1 = numberFromKey(key1), spaces1 = spacesInKey(key1), number2 = numberFromKey(key2), spaces2 = spacesInKey(key2);
        if (number1 % spaces1 !== 0 || number2 % spaces2 !== 0) throw new Error('Client sent invalid Sec-WebSocket-Key headers');
        this._keyValues = [
            number1 / spaces1,
            number2 / spaces2
        ];
        var start = 'HTTP/1.1 101 WebSocket Protocol Handshake', headers = [
            start,
            this._headers.toString(),
            ''
        ];
        return Buffer.from(headers.join('\r\n'), 'binary');
    },
    _handshakeSignature: function() {
        if (this._body.length < this.BODY_SIZE) return null;
        var md5 = crypto.createHash('md5'), buffer = Buffer.allocUnsafe(8 + this.BODY_SIZE);
        buffer.writeUInt32BE(this._keyValues[0], 0);
        buffer.writeUInt32BE(this._keyValues[1], 4);
        Buffer.from(this._body).copy(buffer, 8, 0, this.BODY_SIZE);
        md5.update(buffer);
        return Buffer.from(md5.digest('binary'), 'binary');
    },
    _sendHandshakeBody: function() {
        if (!this._started) return;
        var signature = this._handshakeSignature();
        if (!signature) return;
        this._write(signature);
        this._stage = 0;
        this._open();
        if (this._body.length > this.BODY_SIZE) this.parse(this._body.slice(this.BODY_SIZE));
    },
    _parseLeadingByte: function(octet) {
        if (octet !== 0xFF) return Draft75.prototype._parseLeadingByte.call(this, octet);
        this._closing = true;
        this._length = 0;
        this._stage = 1;
    }
};
for(var key in instance)Draft76.prototype[key] = instance[key];
module.exports = Draft76;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver/server.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), HttpParser = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/http_parser.js [app-ssr] (ecmascript)"), Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Draft75 = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/draft75.js [app-ssr] (ecmascript)"), Draft76 = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/draft76.js [app-ssr] (ecmascript)"), Hybi = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/hybi.js [app-ssr] (ecmascript)");
var Server = function(options) {
    Base.call(this, null, null, options);
    this._http = new HttpParser('request');
};
util.inherits(Server, Base);
var instance = {
    EVENTS: [
        'open',
        'message',
        'error',
        'close',
        'ping',
        'pong'
    ],
    _bindEventListeners: function() {
        this.messages.on('error', function() {});
        this.on('error', function() {});
    },
    parse: function(chunk) {
        if (this._delegate) return this._delegate.parse(chunk);
        this._http.parse(chunk);
        if (!this._http.isComplete()) return;
        this.method = this._http.method;
        this.url = this._http.url;
        this.headers = this._http.headers;
        this.body = this._http.body;
        var self = this;
        this._delegate = Server.http(this, this._options);
        this._delegate.messages = this.messages;
        this._delegate.io = this.io;
        this._open();
        this.EVENTS.forEach(function(event) {
            this._delegate.on(event, function(e) {
                self.emit(event, e);
            });
        }, this);
        this.protocol = this._delegate.protocol;
        this.version = this._delegate.version;
        this.parse(this._http.body);
        this.emit('connect', new Base.ConnectEvent());
    },
    _open: function() {
        this.__queue.forEach(function(msg) {
            this._delegate[msg[0]].apply(this._delegate, msg[1]);
        }, this);
        this.__queue = [];
    }
};
[
    'addExtension',
    'setHeader',
    'start',
    'frame',
    'text',
    'binary',
    'ping',
    'close'
].forEach(function(method) {
    instance[method] = function() {
        if (this._delegate) {
            return this._delegate[method].apply(this._delegate, arguments);
        } else {
            this.__queue.push([
                method,
                arguments
            ]);
            return true;
        }
    };
});
for(var key in instance)Server.prototype[key] = instance[key];
Server.isSecureRequest = function(request) {
    if (request.connection && request.connection.authorized !== undefined) return true;
    if (request.socket && request.socket.secure) return true;
    var headers = request.headers;
    if (!headers) return false;
    if (headers['https'] === 'on') return true;
    if (headers['x-forwarded-ssl'] === 'on') return true;
    if (headers['x-forwarded-scheme'] === 'https') return true;
    if (headers['x-forwarded-proto'] === 'https') return true;
    return false;
};
Server.determineUrl = function(request) {
    var scheme = this.isSecureRequest(request) ? 'wss:' : 'ws:';
    return scheme + '//' + request.headers.host + request.url;
};
Server.http = function(request, options) {
    options = options || {};
    if (options.requireMasking === undefined) options.requireMasking = true;
    var headers = request.headers, version = headers['sec-websocket-version'], key = headers['sec-websocket-key'], key1 = headers['sec-websocket-key1'], key2 = headers['sec-websocket-key2'], url = this.determineUrl(request);
    if (version || key) return new Hybi(request, url, options);
    else if (key1 || key2) return new Draft76(request, url, options);
    else return new Draft75(request, url, options);
};
module.exports = Server;
}}),
"[project]/node_modules/websocket-driver/lib/websocket/driver.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
// Protocol references:
//
// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-75
// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-76
// * http://tools.ietf.org/html/draft-ietf-hybi-thewebsocketprotocol-17
var Base = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/base.js [app-ssr] (ecmascript)"), Client = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/client.js [app-ssr] (ecmascript)"), Server = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/server.js [app-ssr] (ecmascript)");
var Driver = {
    client: function(url, options) {
        options = options || {};
        if (options.masking === undefined) options.masking = true;
        return new Client(url, options);
    },
    server: function(options) {
        options = options || {};
        if (options.requireMasking === undefined) options.requireMasking = true;
        return new Server(options);
    },
    http: function() {
        return Server.http.apply(Server, arguments);
    },
    isSecureRequest: function(request) {
        return Server.isSecureRequest(request);
    },
    isWebSocket: function(request) {
        return Base.isWebSocket(request);
    },
    validateOptions: function(options, validKeys) {
        Base.validateOptions(options, validKeys);
    }
};
module.exports = Driver;
}}),
"[project]/node_modules/http-parser-js/http-parser.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*jshint node:true */ exports.HTTPParser = HTTPParser;
function HTTPParser(type) {
    if (type !== undefined && type !== HTTPParser.REQUEST && type !== HTTPParser.RESPONSE) {
        throw new Error('type must be REQUEST or RESPONSE');
    }
    if (type === undefined) {
    // Node v12+
    } else {
        this.initialize(type);
    }
    this.maxHeaderSize = HTTPParser.maxHeaderSize;
}
HTTPParser.prototype.initialize = function(type, async_resource) {
    if (type !== HTTPParser.REQUEST && type !== HTTPParser.RESPONSE) {
        throw new Error('type must be REQUEST or RESPONSE');
    }
    this.type = type;
    this.state = type + '_LINE';
    this.info = {
        headers: [],
        upgrade: false
    };
    this.trailers = [];
    this.line = '';
    this.isChunked = false;
    this.connection = '';
    this.headerSize = 0; // for preventing too big headers
    this.body_bytes = null;
    this.isUserCall = false;
    this.hadError = false;
};
HTTPParser.encoding = 'ascii';
HTTPParser.maxHeaderSize = 80 * 1024; // maxHeaderSize (in bytes) is configurable, but 80kb by default;
HTTPParser.REQUEST = 'REQUEST';
HTTPParser.RESPONSE = 'RESPONSE';
// Note: *not* starting with kOnHeaders=0 line the Node parser, because any
//   newly added constants (kOnTimeout in Node v12.19.0) will overwrite 0!
var kOnHeaders = HTTPParser.kOnHeaders = 1;
var kOnHeadersComplete = HTTPParser.kOnHeadersComplete = 2;
var kOnBody = HTTPParser.kOnBody = 3;
var kOnMessageComplete = HTTPParser.kOnMessageComplete = 4;
// Some handler stubs, needed for compatibility
HTTPParser.prototype[kOnHeaders] = HTTPParser.prototype[kOnHeadersComplete] = HTTPParser.prototype[kOnBody] = HTTPParser.prototype[kOnMessageComplete] = function() {};
var compatMode0_12 = true;
Object.defineProperty(HTTPParser, 'kOnExecute', {
    get: function() {
        // hack for backward compatibility
        compatMode0_12 = false;
        return 99;
    }
});
var methods = exports.methods = HTTPParser.methods = [
    'DELETE',
    'GET',
    'HEAD',
    'POST',
    'PUT',
    'CONNECT',
    'OPTIONS',
    'TRACE',
    'COPY',
    'LOCK',
    'MKCOL',
    'MOVE',
    'PROPFIND',
    'PROPPATCH',
    'SEARCH',
    'UNLOCK',
    'BIND',
    'REBIND',
    'UNBIND',
    'ACL',
    'REPORT',
    'MKACTIVITY',
    'CHECKOUT',
    'MERGE',
    'M-SEARCH',
    'NOTIFY',
    'SUBSCRIBE',
    'UNSUBSCRIBE',
    'PATCH',
    'PURGE',
    'MKCALENDAR',
    'LINK',
    'UNLINK',
    'SOURCE'
];
var method_connect = methods.indexOf('CONNECT');
HTTPParser.prototype.reinitialize = HTTPParser;
HTTPParser.prototype.close = HTTPParser.prototype.pause = HTTPParser.prototype.resume = HTTPParser.prototype.remove = HTTPParser.prototype.free = function() {};
HTTPParser.prototype._compatMode0_11 = false;
HTTPParser.prototype.getAsyncId = function() {
    return 0;
};
var headerState = {
    REQUEST_LINE: true,
    RESPONSE_LINE: true,
    HEADER: true
};
HTTPParser.prototype.execute = function(chunk, start, length) {
    if (!(this instanceof HTTPParser)) {
        throw new TypeError('not a HTTPParser');
    }
    // backward compat to node < 0.11.4
    // Note: the start and length params were removed in newer version
    start = start || 0;
    length = typeof length === 'number' ? length : chunk.length;
    this.chunk = chunk;
    this.offset = start;
    var end = this.end = start + length;
    try {
        while(this.offset < end){
            if (this[this.state]()) {
                break;
            }
        }
    } catch (err) {
        if (this.isUserCall) {
            throw err;
        }
        this.hadError = true;
        return err;
    }
    this.chunk = null;
    length = this.offset - start;
    if (headerState[this.state]) {
        this.headerSize += length;
        if (this.headerSize > (this.maxHeaderSize || HTTPParser.maxHeaderSize)) {
            return new Error('max header size exceeded');
        }
    }
    return length;
};
var stateFinishAllowed = {
    REQUEST_LINE: true,
    RESPONSE_LINE: true,
    BODY_RAW: true
};
HTTPParser.prototype.finish = function() {
    if (this.hadError) {
        return;
    }
    if (!stateFinishAllowed[this.state]) {
        return new Error('invalid state for EOF');
    }
    if (this.state === 'BODY_RAW') {
        this.userCall()(this[kOnMessageComplete]());
    }
};
// These three methods are used for an internal speed optimization, and it also
// works if theses are noops. Basically consume() asks us to read the bytes
// ourselves, but if we don't do it we get them through execute().
HTTPParser.prototype.consume = HTTPParser.prototype.unconsume = HTTPParser.prototype.getCurrentBuffer = function() {};
//For correct error handling - see HTTPParser#execute
//Usage: this.userCall()(userFunction('arg'));
HTTPParser.prototype.userCall = function() {
    this.isUserCall = true;
    var self = this;
    return function(ret) {
        self.isUserCall = false;
        return ret;
    };
};
HTTPParser.prototype.nextRequest = function() {
    this.userCall()(this[kOnMessageComplete]());
    this.reinitialize(this.type);
};
HTTPParser.prototype.consumeLine = function() {
    var end = this.end, chunk = this.chunk;
    for(var i = this.offset; i < end; i++){
        if (chunk[i] === 0x0a) {
            var line = this.line + chunk.toString(HTTPParser.encoding, this.offset, i);
            if (line.charAt(line.length - 1) === '\r') {
                line = line.substr(0, line.length - 1);
            }
            this.line = '';
            this.offset = i + 1;
            return line;
        }
    }
    //line split over multiple chunks
    this.line += chunk.toString(HTTPParser.encoding, this.offset, this.end);
    this.offset = this.end;
};
var headerExp = /^([^: \t]+):[ \t]*((?:.*[^ \t])|)/;
var headerContinueExp = /^[ \t]+(.*[^ \t])/;
HTTPParser.prototype.parseHeader = function(line, headers) {
    if (line.indexOf('\r') !== -1) {
        throw parseErrorCode('HPE_LF_EXPECTED');
    }
    var match = headerExp.exec(line);
    var k = match && match[1];
    if (k) {
        headers.push(k);
        headers.push(match[2]);
    } else {
        var matchContinue = headerContinueExp.exec(line);
        if (matchContinue && headers.length) {
            if (headers[headers.length - 1]) {
                headers[headers.length - 1] += ' ';
            }
            headers[headers.length - 1] += matchContinue[1];
        }
    }
};
var requestExp = /^([A-Z-]+) ([^ ]+) HTTP\/(\d)\.(\d)$/;
HTTPParser.prototype.REQUEST_LINE = function() {
    var line = this.consumeLine();
    if (!line) {
        return;
    }
    var match = requestExp.exec(line);
    if (match === null) {
        throw parseErrorCode('HPE_INVALID_CONSTANT');
    }
    this.info.method = this._compatMode0_11 ? match[1] : methods.indexOf(match[1]);
    if (this.info.method === -1) {
        throw new Error('invalid request method');
    }
    this.info.url = match[2];
    this.info.versionMajor = +match[3];
    this.info.versionMinor = +match[4];
    this.body_bytes = 0;
    this.state = 'HEADER';
};
var responseExp = /^HTTP\/(\d)\.(\d) (\d{3}) ?(.*)$/;
HTTPParser.prototype.RESPONSE_LINE = function() {
    var line = this.consumeLine();
    if (!line) {
        return;
    }
    var match = responseExp.exec(line);
    if (match === null) {
        throw parseErrorCode('HPE_INVALID_CONSTANT');
    }
    this.info.versionMajor = +match[1];
    this.info.versionMinor = +match[2];
    var statusCode = this.info.statusCode = +match[3];
    this.info.statusMessage = match[4];
    // Implied zero length.
    if ((statusCode / 100 | 0) === 1 || statusCode === 204 || statusCode === 304) {
        this.body_bytes = 0;
    }
    this.state = 'HEADER';
};
HTTPParser.prototype.shouldKeepAlive = function() {
    if (this.info.versionMajor > 0 && this.info.versionMinor > 0) {
        if (this.connection.indexOf('close') !== -1) {
            return false;
        }
    } else if (this.connection.indexOf('keep-alive') === -1) {
        return false;
    }
    if (this.body_bytes !== null || this.isChunked) {
        return true;
    }
    return false;
};
HTTPParser.prototype.HEADER = function() {
    var line = this.consumeLine();
    if (line === undefined) {
        return;
    }
    var info = this.info;
    if (line) {
        this.parseHeader(line, info.headers);
    } else {
        var headers = info.headers;
        var hasContentLength = false;
        var currentContentLengthValue;
        var hasUpgradeHeader = false;
        for(var i = 0; i < headers.length; i += 2){
            switch(headers[i].toLowerCase()){
                case 'transfer-encoding':
                    this.isChunked = headers[i + 1].toLowerCase() === 'chunked';
                    break;
                case 'content-length':
                    currentContentLengthValue = +headers[i + 1];
                    if (hasContentLength) {
                        // Fix duplicate Content-Length header with same values.
                        // Throw error only if values are different.
                        // Known issues:
                        // https://github.com/request/request/issues/2091#issuecomment-328715113
                        // https://github.com/nodejs/node/issues/6517#issuecomment-216263771
                        if (currentContentLengthValue !== this.body_bytes) {
                            throw parseErrorCode('HPE_UNEXPECTED_CONTENT_LENGTH');
                        }
                    } else {
                        hasContentLength = true;
                        this.body_bytes = currentContentLengthValue;
                    }
                    break;
                case 'connection':
                    this.connection += headers[i + 1].toLowerCase();
                    break;
                case 'upgrade':
                    hasUpgradeHeader = true;
                    break;
            }
        }
        // if both isChunked and hasContentLength, isChunked wins
        // This is required so the body is parsed using the chunked method, and matches
        // Chrome's behavior.  We could, maybe, ignore them both (would get chunked
        // encoding into the body), and/or disable shouldKeepAlive to be more
        // resilient.
        if (this.isChunked && hasContentLength) {
            hasContentLength = false;
            this.body_bytes = null;
        }
        // Logic from https://github.com/nodejs/http-parser/blob/921d5585515a153fa00e411cf144280c59b41f90/http_parser.c#L1727-L1737
        // "For responses, "Upgrade: foo" and "Connection: upgrade" are
        //   mandatory only when it is a 101 Switching Protocols response,
        //   otherwise it is purely informational, to announce support.
        if (hasUpgradeHeader && this.connection.indexOf('upgrade') != -1) {
            info.upgrade = this.type === HTTPParser.REQUEST || info.statusCode === 101;
        } else {
            info.upgrade = info.method === method_connect;
        }
        if (this.isChunked && info.upgrade) {
            this.isChunked = false;
        }
        info.shouldKeepAlive = this.shouldKeepAlive();
        //problem which also exists in original node: we should know skipBody before calling onHeadersComplete
        var skipBody;
        if (compatMode0_12) {
            skipBody = this.userCall()(this[kOnHeadersComplete](info));
        } else {
            skipBody = this.userCall()(this[kOnHeadersComplete](info.versionMajor, info.versionMinor, info.headers, info.method, info.url, info.statusCode, info.statusMessage, info.upgrade, info.shouldKeepAlive));
        }
        if (skipBody === 2) {
            this.nextRequest();
            return true;
        } else if (this.isChunked && !skipBody) {
            this.state = 'BODY_CHUNKHEAD';
        } else if (skipBody || this.body_bytes === 0) {
            this.nextRequest();
            // For older versions of node (v6.x and older?), that return skipBody=1 or skipBody=true,
            //   need this "return true;" if it's an upgrade request.
            return info.upgrade;
        } else if (this.body_bytes === null) {
            this.state = 'BODY_RAW';
        } else {
            this.state = 'BODY_SIZED';
        }
    }
};
HTTPParser.prototype.BODY_CHUNKHEAD = function() {
    var line = this.consumeLine();
    if (line === undefined) {
        return;
    }
    this.body_bytes = parseInt(line, 16);
    if (!this.body_bytes) {
        this.state = 'BODY_CHUNKTRAILERS';
    } else {
        this.state = 'BODY_CHUNK';
    }
};
HTTPParser.prototype.BODY_CHUNK = function() {
    var length = Math.min(this.end - this.offset, this.body_bytes);
    // 0, length are for backwards compatibility. See: https://github.com/creationix/http-parser-js/pull/98
    this.userCall()(this[kOnBody](this.chunk.slice(this.offset, this.offset + length), 0, length));
    this.offset += length;
    this.body_bytes -= length;
    if (!this.body_bytes) {
        this.state = 'BODY_CHUNKEMPTYLINE';
    }
};
HTTPParser.prototype.BODY_CHUNKEMPTYLINE = function() {
    var line = this.consumeLine();
    if (line === undefined) {
        return;
    }
    if (line !== '') {
        throw new Error('Expected empty line');
    }
    this.state = 'BODY_CHUNKHEAD';
};
HTTPParser.prototype.BODY_CHUNKTRAILERS = function() {
    var line = this.consumeLine();
    if (line === undefined) {
        return;
    }
    if (line) {
        this.parseHeader(line, this.trailers);
    } else {
        if (this.trailers.length) {
            this.userCall()(this[kOnHeaders](this.trailers, ''));
        }
        this.nextRequest();
    }
};
HTTPParser.prototype.BODY_RAW = function() {
    // 0, length are for backwards compatibility. See: https://github.com/creationix/http-parser-js/pull/98
    this.userCall()(this[kOnBody](this.chunk.slice(this.offset, this.end), 0, this.end - this.offset));
    this.offset = this.end;
};
HTTPParser.prototype.BODY_SIZED = function() {
    var length = Math.min(this.end - this.offset, this.body_bytes);
    // 0, length are for backwards compatibility. See: https://github.com/creationix/http-parser-js/pull/98
    this.userCall()(this[kOnBody](this.chunk.slice(this.offset, this.offset + length), 0, length));
    this.offset += length;
    this.body_bytes -= length;
    if (!this.body_bytes) {
        this.nextRequest();
    }
};
// backward compat to node < 0.11.6
[
    'Headers',
    'HeadersComplete',
    'Body',
    'MessageComplete'
].forEach(function(name) {
    var k = HTTPParser['kOn' + name];
    Object.defineProperty(HTTPParser.prototype, 'on' + name, {
        get: function() {
            return this[k];
        },
        set: function(to) {
            // hack for backward compatibility
            this._compatMode0_11 = true;
            method_connect = 'CONNECT';
            return this[k] = to;
        }
    });
});
function parseErrorCode(code) {
    var err = new Error('Parse Error');
    err.code = code;
    return err;
}
}}),
"[project]/node_modules/websocket-extensions/lib/parser.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var TOKEN = /([!#\$%&'\*\+\-\.\^_`\|~0-9A-Za-z]+)/, NOTOKEN = /([^!#\$%&'\*\+\-\.\^_`\|~0-9A-Za-z])/g, QUOTED = /"((?:\\[\x00-\x7f]|[^\x00-\x08\x0a-\x1f\x7f"\\])*)"/, PARAM = new RegExp(TOKEN.source + '(?:=(?:' + TOKEN.source + '|' + QUOTED.source + '))?'), EXT = new RegExp(TOKEN.source + '(?: *; *' + PARAM.source + ')*', 'g'), EXT_LIST = new RegExp('^' + EXT.source + '(?: *, *' + EXT.source + ')*$'), NUMBER = /^-?(0|[1-9][0-9]*)(\.[0-9]+)?$/;
var hasOwnProperty = Object.prototype.hasOwnProperty;
var Parser = {
    parseHeader: function(header) {
        var offers = new Offers();
        if (header === '' || header === undefined) return offers;
        if (!EXT_LIST.test(header)) throw new SyntaxError('Invalid Sec-WebSocket-Extensions header: ' + header);
        var values = header.match(EXT);
        values.forEach(function(value) {
            var params = value.match(new RegExp(PARAM.source, 'g')), name = params.shift(), offer = {};
            params.forEach(function(param) {
                var args = param.match(PARAM), key = args[1], data;
                if (args[2] !== undefined) {
                    data = args[2];
                } else if (args[3] !== undefined) {
                    data = args[3].replace(/\\/g, '');
                } else {
                    data = true;
                }
                if (NUMBER.test(data)) data = parseFloat(data);
                if (hasOwnProperty.call(offer, key)) {
                    offer[key] = [].concat(offer[key]);
                    offer[key].push(data);
                } else {
                    offer[key] = data;
                }
            }, this);
            offers.push(name, offer);
        }, this);
        return offers;
    },
    serializeParams: function(name, params) {
        var values = [];
        var print = function(key, value) {
            if (value instanceof Array) {
                value.forEach(function(v) {
                    print(key, v);
                });
            } else if (value === true) {
                values.push(key);
            } else if (typeof value === 'number') {
                values.push(key + '=' + value);
            } else if (NOTOKEN.test(value)) {
                values.push(key + '="' + value.replace(/"/g, '\\"') + '"');
            } else {
                values.push(key + '=' + value);
            }
        };
        for(var key in params)print(key, params[key]);
        return [
            name
        ].concat(values).join('; ');
    }
};
var Offers = function() {
    this._byName = {};
    this._inOrder = [];
};
Offers.prototype.push = function(name, params) {
    if (!hasOwnProperty.call(this._byName, name)) this._byName[name] = [];
    this._byName[name].push(params);
    this._inOrder.push({
        name: name,
        params: params
    });
};
Offers.prototype.eachOffer = function(callback, context) {
    var list = this._inOrder;
    for(var i = 0, n = list.length; i < n; i++)callback.call(context, list[i].name, list[i].params);
};
Offers.prototype.byName = function(name) {
    return this._byName[name] || [];
};
Offers.prototype.toArray = function() {
    return this._inOrder.slice();
};
module.exports = Parser;
}}),
"[project]/node_modules/websocket-extensions/lib/pipeline/ring_buffer.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var RingBuffer = function(bufferSize) {
    this._bufferSize = bufferSize;
    this.clear();
};
RingBuffer.prototype.clear = function() {
    this._buffer = new Array(this._bufferSize);
    this._ringOffset = 0;
    this._ringSize = this._bufferSize;
    this._head = 0;
    this._tail = 0;
    this.length = 0;
};
RingBuffer.prototype.push = function(value) {
    var expandBuffer = false, expandRing = false;
    if (this._ringSize < this._bufferSize) {
        expandBuffer = this._tail === 0;
    } else if (this._ringOffset === this._ringSize) {
        expandBuffer = true;
        expandRing = this._tail === 0;
    }
    if (expandBuffer) {
        this._tail = this._bufferSize;
        this._buffer = this._buffer.concat(new Array(this._bufferSize));
        this._bufferSize = this._buffer.length;
        if (expandRing) this._ringSize = this._bufferSize;
    }
    this._buffer[this._tail] = value;
    this.length += 1;
    if (this._tail < this._ringSize) this._ringOffset += 1;
    this._tail = (this._tail + 1) % this._bufferSize;
};
RingBuffer.prototype.peek = function() {
    if (this.length === 0) return void 0;
    return this._buffer[this._head];
};
RingBuffer.prototype.shift = function() {
    if (this.length === 0) return void 0;
    var value = this._buffer[this._head];
    this._buffer[this._head] = void 0;
    this.length -= 1;
    this._ringOffset -= 1;
    if (this._ringOffset === 0 && this.length > 0) {
        this._head = this._ringSize;
        this._ringOffset = this.length;
        this._ringSize = this._bufferSize;
    } else {
        this._head = (this._head + 1) % this._ringSize;
    }
    return value;
};
module.exports = RingBuffer;
}}),
"[project]/node_modules/websocket-extensions/lib/pipeline/functor.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var RingBuffer = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/ring_buffer.js [app-ssr] (ecmascript)");
var Functor = function(session, method) {
    this._session = session;
    this._method = method;
    this._queue = new RingBuffer(Functor.QUEUE_SIZE);
    this._stopped = false;
    this.pending = 0;
};
Functor.QUEUE_SIZE = 8;
Functor.prototype.call = function(error, message, callback, context) {
    if (this._stopped) return;
    var record = {
        error: error,
        message: message,
        callback: callback,
        context: context,
        done: false
    }, called = false, self = this;
    this._queue.push(record);
    if (record.error) {
        record.done = true;
        this._stop();
        return this._flushQueue();
    }
    var handler = function(err, msg) {
        if (!(called ^ (called = true))) return;
        if (err) {
            self._stop();
            record.error = err;
            record.message = null;
        } else {
            record.message = msg;
        }
        record.done = true;
        self._flushQueue();
    };
    try {
        this._session[this._method](message, handler);
    } catch (err) {
        handler(err);
    }
};
Functor.prototype._stop = function() {
    this.pending = this._queue.length;
    this._stopped = true;
};
Functor.prototype._flushQueue = function() {
    var queue = this._queue, record;
    while(queue.length > 0 && queue.peek().done){
        record = queue.shift();
        if (record.error) {
            this.pending = 0;
            queue.clear();
        } else {
            this.pending -= 1;
        }
        record.callback.call(record.context, record.error, record.message);
    }
};
module.exports = Functor;
}}),
"[project]/node_modules/websocket-extensions/lib/pipeline/pledge.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var RingBuffer = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/ring_buffer.js [app-ssr] (ecmascript)");
var Pledge = function() {
    this._complete = false;
    this._callbacks = new RingBuffer(Pledge.QUEUE_SIZE);
};
Pledge.QUEUE_SIZE = 4;
Pledge.all = function(list) {
    var pledge = new Pledge(), pending = list.length, n = pending;
    if (pending === 0) pledge.done();
    while(n--)list[n].then(function() {
        pending -= 1;
        if (pending === 0) pledge.done();
    });
    return pledge;
};
Pledge.prototype.then = function(callback) {
    if (this._complete) callback();
    else this._callbacks.push(callback);
};
Pledge.prototype.done = function() {
    this._complete = true;
    var callbacks = this._callbacks, callback;
    while(callback = callbacks.shift())callback();
};
module.exports = Pledge;
}}),
"[project]/node_modules/websocket-extensions/lib/pipeline/cell.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Functor = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/functor.js [app-ssr] (ecmascript)"), Pledge = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/pledge.js [app-ssr] (ecmascript)");
var Cell = function(tuple) {
    this._ext = tuple[0];
    this._session = tuple[1];
    this._functors = {
        incoming: new Functor(this._session, 'processIncomingMessage'),
        outgoing: new Functor(this._session, 'processOutgoingMessage')
    };
};
Cell.prototype.pending = function(direction) {
    var functor = this._functors[direction];
    if (!functor._stopped) functor.pending += 1;
};
Cell.prototype.incoming = function(error, message, callback, context) {
    this._exec('incoming', error, message, callback, context);
};
Cell.prototype.outgoing = function(error, message, callback, context) {
    this._exec('outgoing', error, message, callback, context);
};
Cell.prototype.close = function() {
    this._closed = this._closed || new Pledge();
    this._doClose();
    return this._closed;
};
Cell.prototype._exec = function(direction, error, message, callback, context) {
    this._functors[direction].call(error, message, function(err, msg) {
        if (err) err.message = this._ext.name + ': ' + err.message;
        callback.call(context, err, msg);
        this._doClose();
    }, this);
};
Cell.prototype._doClose = function() {
    var fin = this._functors.incoming, fout = this._functors.outgoing;
    if (!this._closed || fin.pending + fout.pending !== 0) return;
    if (this._session) this._session.close();
    this._session = null;
    this._closed.done();
};
module.exports = Cell;
}}),
"[project]/node_modules/websocket-extensions/lib/pipeline/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Cell = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/cell.js [app-ssr] (ecmascript)"), Pledge = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/pledge.js [app-ssr] (ecmascript)");
var Pipeline = function(sessions) {
    this._cells = sessions.map(function(session) {
        return new Cell(session);
    });
    this._stopped = {
        incoming: false,
        outgoing: false
    };
};
Pipeline.prototype.processIncomingMessage = function(message, callback, context) {
    if (this._stopped.incoming) return;
    this._loop('incoming', this._cells.length - 1, -1, -1, message, callback, context);
};
Pipeline.prototype.processOutgoingMessage = function(message, callback, context) {
    if (this._stopped.outgoing) return;
    this._loop('outgoing', 0, this._cells.length, 1, message, callback, context);
};
Pipeline.prototype.close = function(callback, context) {
    this._stopped = {
        incoming: true,
        outgoing: true
    };
    var closed = this._cells.map(function(a) {
        return a.close();
    });
    if (callback) Pledge.all(closed).then(function() {
        callback.call(context);
    });
};
Pipeline.prototype._loop = function(direction, start, end, step, message, callback, context) {
    var cells = this._cells, n = cells.length, self = this;
    while(n--)cells[n].pending(direction);
    var pipe = function(index, error, msg) {
        if (index === end) return callback.call(context, error, msg);
        cells[index][direction](error, msg, function(err, m) {
            if (err) self._stopped[direction] = true;
            pipe(index + step, err, m);
        });
    };
    pipe(start, null, message);
};
module.exports = Pipeline;
}}),
"[project]/node_modules/websocket-extensions/lib/websocket_extensions.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Parser = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/parser.js [app-ssr] (ecmascript)"), Pipeline = __turbopack_context__.r("[project]/node_modules/websocket-extensions/lib/pipeline/index.js [app-ssr] (ecmascript)");
var Extensions = function() {
    this._rsv1 = this._rsv2 = this._rsv3 = null;
    this._byName = {};
    this._inOrder = [];
    this._sessions = [];
    this._index = {};
};
Extensions.MESSAGE_OPCODES = [
    1,
    2
];
var instance = {
    add: function(ext) {
        if (typeof ext.name !== 'string') throw new TypeError('extension.name must be a string');
        if (ext.type !== 'permessage') throw new TypeError('extension.type must be "permessage"');
        if (typeof ext.rsv1 !== 'boolean') throw new TypeError('extension.rsv1 must be true or false');
        if (typeof ext.rsv2 !== 'boolean') throw new TypeError('extension.rsv2 must be true or false');
        if (typeof ext.rsv3 !== 'boolean') throw new TypeError('extension.rsv3 must be true or false');
        if (this._byName.hasOwnProperty(ext.name)) throw new TypeError('An extension with name "' + ext.name + '" is already registered');
        this._byName[ext.name] = ext;
        this._inOrder.push(ext);
    },
    generateOffer: function() {
        var sessions = [], offer = [], index = {};
        this._inOrder.forEach(function(ext) {
            var session = ext.createClientSession();
            if (!session) return;
            var record = [
                ext,
                session
            ];
            sessions.push(record);
            index[ext.name] = record;
            var offers = session.generateOffer();
            offers = offers ? [].concat(offers) : [];
            offers.forEach(function(off) {
                offer.push(Parser.serializeParams(ext.name, off));
            }, this);
        }, this);
        this._sessions = sessions;
        this._index = index;
        return offer.length > 0 ? offer.join(', ') : null;
    },
    activate: function(header) {
        var responses = Parser.parseHeader(header), sessions = [];
        responses.eachOffer(function(name, params) {
            var record = this._index[name];
            if (!record) throw new Error('Server sent an extension response for unknown extension "' + name + '"');
            var ext = record[0], session = record[1], reserved = this._reserved(ext);
            if (reserved) throw new Error('Server sent two extension responses that use the RSV' + reserved[0] + ' bit: "' + reserved[1] + '" and "' + ext.name + '"');
            if (session.activate(params) !== true) throw new Error('Server sent unacceptable extension parameters: ' + Parser.serializeParams(name, params));
            this._reserve(ext);
            sessions.push(record);
        }, this);
        this._sessions = sessions;
        this._pipeline = new Pipeline(sessions);
    },
    generateResponse: function(header) {
        var sessions = [], response = [], offers = Parser.parseHeader(header);
        this._inOrder.forEach(function(ext) {
            var offer = offers.byName(ext.name);
            if (offer.length === 0 || this._reserved(ext)) return;
            var session = ext.createServerSession(offer);
            if (!session) return;
            this._reserve(ext);
            sessions.push([
                ext,
                session
            ]);
            response.push(Parser.serializeParams(ext.name, session.generateResponse()));
        }, this);
        this._sessions = sessions;
        this._pipeline = new Pipeline(sessions);
        return response.length > 0 ? response.join(', ') : null;
    },
    validFrameRsv: function(frame) {
        var allowed = {
            rsv1: false,
            rsv2: false,
            rsv3: false
        }, ext;
        if (Extensions.MESSAGE_OPCODES.indexOf(frame.opcode) >= 0) {
            for(var i = 0, n = this._sessions.length; i < n; i++){
                ext = this._sessions[i][0];
                allowed.rsv1 = allowed.rsv1 || ext.rsv1;
                allowed.rsv2 = allowed.rsv2 || ext.rsv2;
                allowed.rsv3 = allowed.rsv3 || ext.rsv3;
            }
        }
        return (allowed.rsv1 || !frame.rsv1) && (allowed.rsv2 || !frame.rsv2) && (allowed.rsv3 || !frame.rsv3);
    },
    processIncomingMessage: function(message, callback, context) {
        this._pipeline.processIncomingMessage(message, callback, context);
    },
    processOutgoingMessage: function(message, callback, context) {
        this._pipeline.processOutgoingMessage(message, callback, context);
    },
    close: function(callback, context) {
        if (!this._pipeline) return callback.call(context);
        this._pipeline.close(callback, context);
    },
    _reserve: function(ext) {
        this._rsv1 = this._rsv1 || ext.rsv1 && ext.name;
        this._rsv2 = this._rsv2 || ext.rsv2 && ext.name;
        this._rsv3 = this._rsv3 || ext.rsv3 && ext.name;
    },
    _reserved: function(ext) {
        if (this._rsv1 && ext.rsv1) return [
            1,
            this._rsv1
        ];
        if (this._rsv2 && ext.rsv2) return [
            2,
            this._rsv2
        ];
        if (this._rsv3 && ext.rsv3) return [
            3,
            this._rsv3
        ];
        return false;
    }
};
for(var key in instance)Extensions.prototype[key] = instance[key];
module.exports = Extensions;
}}),
"[project]/node_modules/faye-websocket/lib/faye/websocket/api/event.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Event = function(eventType, options) {
    this.type = eventType;
    for(var key in options)this[key] = options[key];
};
Event.prototype.initEvent = function(eventType, canBubble, cancelable) {
    this.type = eventType;
    this.bubbles = canBubble;
    this.cancelable = cancelable;
};
Event.prototype.stopPropagation = function() {};
Event.prototype.preventDefault = function() {};
Event.CAPTURING_PHASE = 1;
Event.AT_TARGET = 2;
Event.BUBBLING_PHASE = 3;
module.exports = Event;
}}),
"[project]/node_modules/faye-websocket/lib/faye/websocket/api/event_target.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Event = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event.js [app-ssr] (ecmascript)");
var EventTarget = {
    onopen: null,
    onmessage: null,
    onerror: null,
    onclose: null,
    addEventListener: function(eventType, listener, useCapture) {
        this.on(eventType, listener);
    },
    removeEventListener: function(eventType, listener, useCapture) {
        this.removeListener(eventType, listener);
    },
    dispatchEvent: function(event) {
        event.target = event.currentTarget = this;
        event.eventPhase = Event.AT_TARGET;
        if (this['on' + event.type]) this['on' + event.type](event);
        this.emit(event.type, event);
    }
};
module.exports = EventTarget;
}}),
"[project]/node_modules/faye-websocket/lib/faye/websocket/api.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").Stream, util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), driver = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver.js [app-ssr] (ecmascript)"), EventTarget = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event_target.js [app-ssr] (ecmascript)"), Event = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event.js [app-ssr] (ecmascript)");
var API = function(options) {
    options = options || {};
    driver.validateOptions(options, [
        'headers',
        'extensions',
        'maxLength',
        'ping',
        'proxy',
        'tls',
        'ca'
    ]);
    this.readable = this.writable = true;
    var headers = options.headers;
    if (headers) {
        for(var name in headers)this._driver.setHeader(name, headers[name]);
    }
    var extensions = options.extensions;
    if (extensions) {
        [].concat(extensions).forEach(this._driver.addExtension, this._driver);
    }
    this._ping = options.ping;
    this._pingId = 0;
    this.readyState = API.CONNECTING;
    this.bufferedAmount = 0;
    this.protocol = '';
    this.url = this._driver.url;
    this.version = this._driver.version;
    var self = this;
    this._driver.on('open', function(e) {
        self._open();
    });
    this._driver.on('message', function(e) {
        self._receiveMessage(e.data);
    });
    this._driver.on('close', function(e) {
        self._beginClose(e.reason, e.code);
    });
    this._driver.on('error', function(error) {
        self._emitError(error.message);
    });
    this.on('error', function() {});
    this._driver.messages.on('drain', function() {
        self.emit('drain');
    });
    if (this._ping) this._pingTimer = setInterval(function() {
        self._pingId += 1;
        self.ping(self._pingId.toString());
    }, this._ping * 1000);
    this._configureStream();
    if (!this._proxy) {
        this._stream.pipe(this._driver.io);
        this._driver.io.pipe(this._stream);
    }
};
util.inherits(API, Stream);
API.CONNECTING = 0;
API.OPEN = 1;
API.CLOSING = 2;
API.CLOSED = 3;
API.CLOSE_TIMEOUT = 30000;
var instance = {
    write: function(data) {
        return this.send(data);
    },
    end: function(data) {
        if (data !== undefined) this.send(data);
        this.close();
    },
    pause: function() {
        return this._driver.messages.pause();
    },
    resume: function() {
        return this._driver.messages.resume();
    },
    send: function(data) {
        if (this.readyState > API.OPEN) return false;
        if (!(data instanceof Buffer)) data = String(data);
        return this._driver.messages.write(data);
    },
    ping: function(message, callback) {
        if (this.readyState > API.OPEN) return false;
        return this._driver.ping(message, callback);
    },
    close: function(code, reason) {
        if (code === undefined) code = 1000;
        if (reason === undefined) reason = '';
        if (code !== 1000 && (code < 3000 || code > 4999)) throw new Error("Failed to execute 'close' on WebSocket: " + "The code must be either 1000, or between 3000 and 4999. " + code + " is neither.");
        if (this.readyState < API.CLOSING) {
            var self = this;
            this._closeTimer = setTimeout(function() {
                self._beginClose('', 1006);
            }, API.CLOSE_TIMEOUT);
        }
        if (this.readyState !== API.CLOSED) this.readyState = API.CLOSING;
        this._driver.close(reason, code);
    },
    _configureStream: function() {
        var self = this;
        this._stream.setTimeout(0);
        this._stream.setNoDelay(true);
        [
            'close',
            'end'
        ].forEach(function(event) {
            this._stream.on(event, function() {
                self._finalizeClose();
            });
        }, this);
        this._stream.on('error', function(error) {
            self._emitError('Network error: ' + self.url + ': ' + error.message);
            self._finalizeClose();
        });
    },
    _open: function() {
        if (this.readyState !== API.CONNECTING) return;
        this.readyState = API.OPEN;
        this.protocol = this._driver.protocol || '';
        var event = new Event('open');
        event.initEvent('open', false, false);
        this.dispatchEvent(event);
    },
    _receiveMessage: function(data) {
        if (this.readyState > API.OPEN) return false;
        if (this.readable) this.emit('data', data);
        var event = new Event('message', {
            data: data
        });
        event.initEvent('message', false, false);
        this.dispatchEvent(event);
    },
    _emitError: function(message) {
        if (this.readyState >= API.CLOSING) return;
        var event = new Event('error', {
            message: message
        });
        event.initEvent('error', false, false);
        this.dispatchEvent(event);
    },
    _beginClose: function(reason, code) {
        if (this.readyState === API.CLOSED) return;
        this.readyState = API.CLOSING;
        this._closeParams = [
            reason,
            code
        ];
        if (this._stream) {
            this._stream.destroy();
            if (!this._stream.readable) this._finalizeClose();
        }
    },
    _finalizeClose: function() {
        if (this.readyState === API.CLOSED) return;
        this.readyState = API.CLOSED;
        if (this._closeTimer) clearTimeout(this._closeTimer);
        if (this._pingTimer) clearInterval(this._pingTimer);
        if (this._stream) this._stream.end();
        if (this.readable) this.emit('end');
        this.readable = this.writable = false;
        var reason = this._closeParams ? this._closeParams[0] : '', code = this._closeParams ? this._closeParams[1] : 1006;
        var event = new Event('close', {
            code: code,
            reason: reason
        });
        event.initEvent('close', false, false);
        this.dispatchEvent(event);
    }
};
for(var method in instance)API.prototype[method] = instance[method];
for(var key in EventTarget)API.prototype[key] = EventTarget[key];
module.exports = API;
}}),
"[project]/node_modules/faye-websocket/lib/faye/websocket/client.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), net = __turbopack_context__.r("[externals]/net [external] (net, cjs)"), tls = __turbopack_context__.r("[externals]/tls [external] (tls, cjs)"), url = __turbopack_context__.r("[externals]/url [external] (url, cjs)"), driver = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver.js [app-ssr] (ecmascript)"), API = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api.js [app-ssr] (ecmascript)"), Event = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event.js [app-ssr] (ecmascript)");
var DEFAULT_PORTS = {
    'http:': 80,
    'https:': 443,
    'ws:': 80,
    'wss:': 443
}, SECURE_PROTOCOLS = [
    'https:',
    'wss:'
];
var Client = function(_url, protocols, options) {
    options = options || {};
    this.url = _url;
    this._driver = driver.client(this.url, {
        maxLength: options.maxLength,
        protocols: protocols
    });
    [
        'open',
        'error'
    ].forEach(function(event) {
        this._driver.on(event, function() {
            self.headers = self._driver.headers;
            self.statusCode = self._driver.statusCode;
        });
    }, this);
    var proxy = options.proxy || {}, endpoint = url.parse(proxy.origin || this.url), port = endpoint.port || DEFAULT_PORTS[endpoint.protocol], secure = SECURE_PROTOCOLS.indexOf(endpoint.protocol) >= 0, onConnect = function() {
        self._onConnect();
    }, netOptions = options.net || {}, originTLS = options.tls || {}, socketTLS = proxy.origin ? proxy.tls || {} : originTLS, self = this;
    netOptions.host = socketTLS.host = endpoint.hostname;
    netOptions.port = socketTLS.port = port;
    originTLS.ca = originTLS.ca || options.ca;
    socketTLS.servername = socketTLS.servername || endpoint.hostname;
    this._stream = secure ? tls.connect(socketTLS, onConnect) : net.connect(netOptions, onConnect);
    if (proxy.origin) this._configureProxy(proxy, originTLS);
    API.call(this, options);
};
util.inherits(Client, API);
Client.prototype._onConnect = function() {
    var worker = this._proxy || this._driver;
    worker.start();
};
Client.prototype._configureProxy = function(proxy, originTLS) {
    var uri = url.parse(this.url), secure = SECURE_PROTOCOLS.indexOf(uri.protocol) >= 0, self = this, name;
    this._proxy = this._driver.proxy(proxy.origin);
    if (proxy.headers) {
        for(name in proxy.headers)this._proxy.setHeader(name, proxy.headers[name]);
    }
    this._proxy.pipe(this._stream, {
        end: false
    });
    this._stream.pipe(this._proxy);
    this._proxy.on('connect', function() {
        if (secure) {
            var options = {
                socket: self._stream,
                servername: uri.hostname
            };
            for(name in originTLS)options[name] = originTLS[name];
            self._stream = tls.connect(options);
            self._configureStream();
        }
        self._driver.io.pipe(self._stream);
        self._stream.pipe(self._driver.io);
        self._driver.start();
    });
    this._proxy.on('error', function(error) {
        self._driver.emit('error', error);
    });
};
module.exports = Client;
}}),
"[project]/node_modules/faye-websocket/lib/faye/eventsource.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").Stream, util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), driver = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver.js [app-ssr] (ecmascript)"), Headers = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver/headers.js [app-ssr] (ecmascript)"), API = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api.js [app-ssr] (ecmascript)"), EventTarget = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event_target.js [app-ssr] (ecmascript)"), Event = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api/event.js [app-ssr] (ecmascript)");
var EventSource = function(request, response, options) {
    this.writable = true;
    options = options || {};
    this._stream = response.socket;
    this._ping = options.ping || this.DEFAULT_PING;
    this._retry = options.retry || this.DEFAULT_RETRY;
    var scheme = driver.isSecureRequest(request) ? 'https:' : 'http:';
    this.url = scheme + '//' + request.headers.host + request.url;
    this.lastEventId = request.headers['last-event-id'] || '';
    this.readyState = API.CONNECTING;
    var headers = new Headers(), self = this;
    if (options.headers) {
        for(var key in options.headers)headers.set(key, options.headers[key]);
    }
    if (!this._stream || !this._stream.writable) return;
    process.nextTick(function() {
        self._open();
    });
    this._stream.setTimeout(0);
    this._stream.setNoDelay(true);
    var handshake = 'HTTP/1.1 200 OK\r\n' + 'Content-Type: text/event-stream\r\n' + 'Cache-Control: no-cache, no-store\r\n' + 'Connection: close\r\n' + headers.toString() + '\r\n' + 'retry: ' + Math.floor(this._retry * 1000) + '\r\n\r\n';
    this._write(handshake);
    this._stream.on('drain', function() {
        self.emit('drain');
    });
    if (this._ping) this._pingTimer = setInterval(function() {
        self.ping();
    }, this._ping * 1000);
    [
        'error',
        'end'
    ].forEach(function(event) {
        self._stream.on(event, function() {
            self.close();
        });
    });
};
util.inherits(EventSource, Stream);
EventSource.isEventSource = function(request) {
    if (request.method !== 'GET') return false;
    var accept = (request.headers.accept || '').split(/\s*,\s*/);
    return accept.indexOf('text/event-stream') >= 0;
};
var instance = {
    DEFAULT_PING: 10,
    DEFAULT_RETRY: 5,
    _write: function(chunk) {
        if (!this.writable) return false;
        try {
            return this._stream.write(chunk, 'utf8');
        } catch (e) {
            return false;
        }
    },
    _open: function() {
        if (this.readyState !== API.CONNECTING) return;
        this.readyState = API.OPEN;
        var event = new Event('open');
        event.initEvent('open', false, false);
        this.dispatchEvent(event);
    },
    write: function(message) {
        return this.send(message);
    },
    end: function(message) {
        if (message !== undefined) this.write(message);
        this.close();
    },
    send: function(message, options) {
        if (this.readyState > API.OPEN) return false;
        message = String(message).replace(/(\r\n|\r|\n)/g, '$1data: ');
        options = options || {};
        var frame = '';
        if (options.event) frame += 'event: ' + options.event + '\r\n';
        if (options.id) frame += 'id: ' + options.id + '\r\n';
        frame += 'data: ' + message + '\r\n\r\n';
        return this._write(frame);
    },
    ping: function() {
        return this._write(':\r\n\r\n');
    },
    close: function() {
        if (this.readyState > API.OPEN) return false;
        this.readyState = API.CLOSED;
        this.writable = false;
        if (this._pingTimer) clearInterval(this._pingTimer);
        if (this._stream) this._stream.end();
        var event = new Event('close');
        event.initEvent('close', false, false);
        this.dispatchEvent(event);
        return true;
    }
};
for(var method in instance)EventSource.prototype[method] = instance[method];
for(var key in EventTarget)EventSource.prototype[key] = EventTarget[key];
module.exports = EventSource;
}}),
"[project]/node_modules/faye-websocket/lib/faye/websocket.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// API references:
//
// * https://html.spec.whatwg.org/multipage/comms.html#network
// * https://dom.spec.whatwg.org/#interface-eventtarget
// * https://dom.spec.whatwg.org/#interface-event
'use strict';
var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), driver = __turbopack_context__.r("[project]/node_modules/websocket-driver/lib/websocket/driver.js [app-ssr] (ecmascript)"), API = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/api.js [app-ssr] (ecmascript)");
var WebSocket = function(request, socket, body, protocols, options) {
    options = options || {};
    this._stream = socket;
    this._driver = driver.http(request, {
        maxLength: options.maxLength,
        protocols: protocols
    });
    var self = this;
    if (!this._stream || !this._stream.writable) return;
    if (!this._stream.readable) return this._stream.end();
    var catchup = function() {
        self._stream.removeListener('data', catchup);
    };
    this._stream.on('data', catchup);
    API.call(this, options);
    process.nextTick(function() {
        self._driver.start();
        self._driver.io.write(body);
    });
};
util.inherits(WebSocket, API);
WebSocket.isWebSocket = function(request) {
    return driver.isWebSocket(request);
};
WebSocket.validateOptions = function(options, validKeys) {
    driver.validateOptions(options, validKeys);
};
WebSocket.WebSocket = WebSocket;
WebSocket.Client = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/websocket/client.js [app-ssr] (ecmascript)");
WebSocket.EventSource = __turbopack_context__.r("[project]/node_modules/faye-websocket/lib/faye/eventsource.js [app-ssr] (ecmascript)");
module.exports = WebSocket;
}}),
"[project]/node_modules/@firebase/util/dist/postinstall.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDefaultsFromPostinstall": (()=>getDefaultsFromPostinstall)
});
const getDefaultsFromPostinstall = ()=>undefined;
;
}}),
"[project]/node_modules/@firebase/util/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CONSTANTS": (()=>CONSTANTS),
    "DecodeBase64StringError": (()=>DecodeBase64StringError),
    "Deferred": (()=>Deferred),
    "ErrorFactory": (()=>ErrorFactory),
    "FirebaseError": (()=>FirebaseError),
    "MAX_VALUE_MILLIS": (()=>MAX_VALUE_MILLIS),
    "RANDOM_FACTOR": (()=>RANDOM_FACTOR),
    "Sha1": (()=>Sha1),
    "areCookiesEnabled": (()=>areCookiesEnabled),
    "assert": (()=>assert),
    "assertionError": (()=>assertionError),
    "async": (()=>async),
    "base64": (()=>base64),
    "base64Decode": (()=>base64Decode),
    "base64Encode": (()=>base64Encode),
    "base64urlEncodeWithoutPadding": (()=>base64urlEncodeWithoutPadding),
    "calculateBackoffMillis": (()=>calculateBackoffMillis),
    "contains": (()=>contains),
    "createMockUserToken": (()=>createMockUserToken),
    "createSubscribe": (()=>createSubscribe),
    "decode": (()=>decode),
    "deepCopy": (()=>deepCopy),
    "deepEqual": (()=>deepEqual),
    "deepExtend": (()=>deepExtend),
    "errorPrefix": (()=>errorPrefix),
    "extractQuerystring": (()=>extractQuerystring),
    "getDefaultAppConfig": (()=>getDefaultAppConfig),
    "getDefaultEmulatorHost": (()=>getDefaultEmulatorHost),
    "getDefaultEmulatorHostnameAndPort": (()=>getDefaultEmulatorHostnameAndPort),
    "getDefaults": (()=>getDefaults),
    "getExperimentalSetting": (()=>getExperimentalSetting),
    "getGlobal": (()=>getGlobal),
    "getModularInstance": (()=>getModularInstance),
    "getUA": (()=>getUA),
    "isAdmin": (()=>isAdmin),
    "isBrowser": (()=>isBrowser),
    "isBrowserExtension": (()=>isBrowserExtension),
    "isCloudWorkstation": (()=>isCloudWorkstation),
    "isCloudflareWorker": (()=>isCloudflareWorker),
    "isElectron": (()=>isElectron),
    "isEmpty": (()=>isEmpty),
    "isIE": (()=>isIE),
    "isIndexedDBAvailable": (()=>isIndexedDBAvailable),
    "isMobileCordova": (()=>isMobileCordova),
    "isNode": (()=>isNode),
    "isNodeSdk": (()=>isNodeSdk),
    "isReactNative": (()=>isReactNative),
    "isSafari": (()=>isSafari),
    "isSafariOrWebkit": (()=>isSafariOrWebkit),
    "isUWP": (()=>isUWP),
    "isValidFormat": (()=>isValidFormat),
    "isValidTimestamp": (()=>isValidTimestamp),
    "isWebWorker": (()=>isWebWorker),
    "issuedAtTime": (()=>issuedAtTime),
    "jsonEval": (()=>jsonEval),
    "map": (()=>map),
    "ordinal": (()=>ordinal),
    "pingServer": (()=>pingServer),
    "promiseWithTimeout": (()=>promiseWithTimeout),
    "querystring": (()=>querystring),
    "querystringDecode": (()=>querystringDecode),
    "safeGet": (()=>safeGet),
    "stringLength": (()=>stringLength),
    "stringToByteArray": (()=>stringToByteArray),
    "stringify": (()=>stringify),
    "updateEmulatorBanner": (()=>updateEmulatorBanner),
    "validateArgCount": (()=>validateArgCount),
    "validateCallback": (()=>validateCallback),
    "validateContextObject": (()=>validateContextObject),
    "validateIndexedDBOpenable": (()=>validateIndexedDBOpenable),
    "validateNamespace": (()=>validateNamespace)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$postinstall$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/util/dist/postinstall.mjs [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.
 */ const CONSTANTS = {
    /**
     * @define {boolean} Whether this is the client Node.js SDK.
     */ NODE_CLIENT: false,
    /**
     * @define {boolean} Whether this is the Admin Node.js SDK.
     */ NODE_ADMIN: false,
    /**
     * Firebase SDK Version
     */ SDK_VERSION: '${JSCORE_VERSION}'
};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Throws an error if the provided assertion is falsy
 */ const assert = function(assertion, message) {
    if (!assertion) {
        throw assertionError(message);
    }
};
/**
 * Returns an Error object suitable for throwing.
 */ const assertionError = function(message) {
    return new Error('Firebase Database (' + CONSTANTS.SDK_VERSION + ') INTERNAL ASSERT FAILED: ' + message);
};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const stringToByteArray$1 = function(str) {
    // TODO(user): Use native implementations if/when available
    const out = [];
    let p = 0;
    for(let i = 0; i < str.length; i++){
        let c = str.charCodeAt(i);
        if (c < 128) {
            out[p++] = c;
        } else if (c < 2048) {
            out[p++] = c >> 6 | 192;
            out[p++] = c & 63 | 128;
        } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {
            // Surrogate Pair
            c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);
            out[p++] = c >> 18 | 240;
            out[p++] = c >> 12 & 63 | 128;
            out[p++] = c >> 6 & 63 | 128;
            out[p++] = c & 63 | 128;
        } else {
            out[p++] = c >> 12 | 224;
            out[p++] = c >> 6 & 63 | 128;
            out[p++] = c & 63 | 128;
        }
    }
    return out;
};
/**
 * Turns an array of numbers into the string given by the concatenation of the
 * characters to which the numbers correspond.
 * @param bytes Array of numbers representing characters.
 * @return Stringification of the array.
 */ const byteArrayToString = function(bytes) {
    // TODO(user): Use native implementations if/when available
    const out = [];
    let pos = 0, c = 0;
    while(pos < bytes.length){
        const c1 = bytes[pos++];
        if (c1 < 128) {
            out[c++] = String.fromCharCode(c1);
        } else if (c1 > 191 && c1 < 224) {
            const c2 = bytes[pos++];
            out[c++] = String.fromCharCode((c1 & 31) << 6 | c2 & 63);
        } else if (c1 > 239 && c1 < 365) {
            // Surrogate Pair
            const c2 = bytes[pos++];
            const c3 = bytes[pos++];
            const c4 = bytes[pos++];
            const u = ((c1 & 7) << 18 | (c2 & 63) << 12 | (c3 & 63) << 6 | c4 & 63) - 0x10000;
            out[c++] = String.fromCharCode(0xd800 + (u >> 10));
            out[c++] = String.fromCharCode(0xdc00 + (u & 1023));
        } else {
            const c2 = bytes[pos++];
            const c3 = bytes[pos++];
            out[c++] = String.fromCharCode((c1 & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
        }
    }
    return out.join('');
};
// We define it as an object literal instead of a class because a class compiled down to es5 can't
// be treeshaked. https://github.com/rollup/rollup/issues/1691
// Static lookup maps, lazily populated by init_()
// TODO(dlarocque): Define this as a class, since we no longer target ES5.
const base64 = {
    /**
     * Maps bytes to characters.
     */ byteToCharMap_: null,
    /**
     * Maps characters to bytes.
     */ charToByteMap_: null,
    /**
     * Maps bytes to websafe characters.
     * @private
     */ byteToCharMapWebSafe_: null,
    /**
     * Maps websafe characters to bytes.
     * @private
     */ charToByteMapWebSafe_: null,
    /**
     * Our default alphabet, shared between
     * ENCODED_VALS and ENCODED_VALS_WEBSAFE
     */ ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',
    /**
     * Our default alphabet. Value 64 (=) is special; it means "nothing."
     */ get ENCODED_VALS () {
        return this.ENCODED_VALS_BASE + '+/=';
    },
    /**
     * Our websafe alphabet.
     */ get ENCODED_VALS_WEBSAFE () {
        return this.ENCODED_VALS_BASE + '-_.';
    },
    /**
     * Whether this browser supports the atob and btoa functions. This extension
     * started at Mozilla but is now implemented by many browsers. We use the
     * ASSUME_* variables to avoid pulling in the full useragent detection library
     * but still allowing the standard per-browser compilations.
     *
     */ HAS_NATIVE_SUPPORT: typeof atob === 'function',
    /**
     * Base64-encode an array of bytes.
     *
     * @param input An array of bytes (numbers with
     *     value in [0, 255]) to encode.
     * @param webSafe Boolean indicating we should use the
     *     alternative alphabet.
     * @return The base64 encoded string.
     */ encodeByteArray (input, webSafe) {
        if (!Array.isArray(input)) {
            throw Error('encodeByteArray takes an array as a parameter');
        }
        this.init_();
        const byteToCharMap = webSafe ? this.byteToCharMapWebSafe_ : this.byteToCharMap_;
        const output = [];
        for(let i = 0; i < input.length; i += 3){
            const byte1 = input[i];
            const haveByte2 = i + 1 < input.length;
            const byte2 = haveByte2 ? input[i + 1] : 0;
            const haveByte3 = i + 2 < input.length;
            const byte3 = haveByte3 ? input[i + 2] : 0;
            const outByte1 = byte1 >> 2;
            const outByte2 = (byte1 & 0x03) << 4 | byte2 >> 4;
            let outByte3 = (byte2 & 0x0f) << 2 | byte3 >> 6;
            let outByte4 = byte3 & 0x3f;
            if (!haveByte3) {
                outByte4 = 64;
                if (!haveByte2) {
                    outByte3 = 64;
                }
            }
            output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);
        }
        return output.join('');
    },
    /**
     * Base64-encode a string.
     *
     * @param input A string to encode.
     * @param webSafe If true, we should use the
     *     alternative alphabet.
     * @return The base64 encoded string.
     */ encodeString (input, webSafe) {
        // Shortcut for Mozilla browsers that implement
        // a native base64 encoder in the form of "btoa/atob"
        if (this.HAS_NATIVE_SUPPORT && !webSafe) {
            return btoa(input);
        }
        return this.encodeByteArray(stringToByteArray$1(input), webSafe);
    },
    /**
     * Base64-decode a string.
     *
     * @param input to decode.
     * @param webSafe True if we should use the
     *     alternative alphabet.
     * @return string representing the decoded value.
     */ decodeString (input, webSafe) {
        // Shortcut for Mozilla browsers that implement
        // a native base64 encoder in the form of "btoa/atob"
        if (this.HAS_NATIVE_SUPPORT && !webSafe) {
            return atob(input);
        }
        return byteArrayToString(this.decodeStringToByteArray(input, webSafe));
    },
    /**
     * Base64-decode a string.
     *
     * In base-64 decoding, groups of four characters are converted into three
     * bytes.  If the encoder did not apply padding, the input length may not
     * be a multiple of 4.
     *
     * In this case, the last group will have fewer than 4 characters, and
     * padding will be inferred.  If the group has one or two characters, it decodes
     * to one byte.  If the group has three characters, it decodes to two bytes.
     *
     * @param input Input to decode.
     * @param webSafe True if we should use the web-safe alphabet.
     * @return bytes representing the decoded value.
     */ decodeStringToByteArray (input, webSafe) {
        this.init_();
        const charToByteMap = webSafe ? this.charToByteMapWebSafe_ : this.charToByteMap_;
        const output = [];
        for(let i = 0; i < input.length;){
            const byte1 = charToByteMap[input.charAt(i++)];
            const haveByte2 = i < input.length;
            const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;
            ++i;
            const haveByte3 = i < input.length;
            const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;
            ++i;
            const haveByte4 = i < input.length;
            const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;
            ++i;
            if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {
                throw new DecodeBase64StringError();
            }
            const outByte1 = byte1 << 2 | byte2 >> 4;
            output.push(outByte1);
            if (byte3 !== 64) {
                const outByte2 = byte2 << 4 & 0xf0 | byte3 >> 2;
                output.push(outByte2);
                if (byte4 !== 64) {
                    const outByte3 = byte3 << 6 & 0xc0 | byte4;
                    output.push(outByte3);
                }
            }
        }
        return output;
    },
    /**
     * Lazy static initialization function. Called before
     * accessing any of the static map variables.
     * @private
     */ init_ () {
        if (!this.byteToCharMap_) {
            this.byteToCharMap_ = {};
            this.charToByteMap_ = {};
            this.byteToCharMapWebSafe_ = {};
            this.charToByteMapWebSafe_ = {};
            // We want quick mappings back and forth, so we precompute two maps.
            for(let i = 0; i < this.ENCODED_VALS.length; i++){
                this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);
                this.charToByteMap_[this.byteToCharMap_[i]] = i;
                this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);
                this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;
                // Be forgiving when decoding and correctly decode both encodings.
                if (i >= this.ENCODED_VALS_BASE.length) {
                    this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;
                    this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;
                }
            }
        }
    }
};
/**
 * An error encountered while decoding base64 string.
 */ class DecodeBase64StringError extends Error {
    constructor(){
        super(...arguments);
        this.name = 'DecodeBase64StringError';
    }
}
/**
 * URL-safe base64 encoding
 */ const base64Encode = function(str) {
    const utf8Bytes = stringToByteArray$1(str);
    return base64.encodeByteArray(utf8Bytes, true);
};
/**
 * URL-safe base64 encoding (without "." padding in the end).
 * e.g. Used in JSON Web Token (JWT) parts.
 */ const base64urlEncodeWithoutPadding = function(str) {
    // Use base64url encoding and remove padding in the end (dot characters).
    return base64Encode(str).replace(/\./g, '');
};
/**
 * URL-safe base64 decoding
 *
 * NOTE: DO NOT use the global atob() function - it does NOT support the
 * base64Url variant encoding.
 *
 * @param str To be decoded
 * @return Decoded result, if possible
 */ const base64Decode = function(str) {
    try {
        return base64.decodeString(str, true);
    } catch (e) {
        console.error('base64Decode failed: ', e);
    }
    return null;
};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Do a deep-copy of basic JavaScript Objects or Arrays.
 */ function deepCopy(value) {
    return deepExtend(undefined, value);
}
/**
 * Copy properties from source to target (recursively allows extension
 * of Objects and Arrays).  Scalar values in the target are over-written.
 * If target is undefined, an object of the appropriate type will be created
 * (and returned).
 *
 * We recursively copy all child properties of plain Objects in the source- so
 * that namespace- like dictionaries are merged.
 *
 * Note that the target can be a function, in which case the properties in
 * the source Object are copied onto it as static properties of the Function.
 *
 * Note: we don't merge __proto__ to prevent prototype pollution
 */ function deepExtend(target, source) {
    if (!(source instanceof Object)) {
        return source;
    }
    switch(source.constructor){
        case Date:
            // Treat Dates like scalars; if the target date object had any child
            // properties - they will be lost!
            const dateValue = source;
            return new Date(dateValue.getTime());
        case Object:
            if (target === undefined) {
                target = {};
            }
            break;
        case Array:
            // Always copy the array source and overwrite the target.
            target = [];
            break;
        default:
            // Not a plain Object - treat it as a scalar.
            return source;
    }
    for(const prop in source){
        // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202
        if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {
            continue;
        }
        target[prop] = deepExtend(target[prop], source[prop]);
    }
    return target;
}
function isValidKey(key) {
    return key !== '__proto__';
}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Polyfill for `globalThis` object.
 * @returns the `globalThis` object for the given environment.
 * @public
 */ function getGlobal() {
    if (typeof self !== 'undefined') {
        return self;
    }
    if (typeof window !== 'undefined') {
        return window;
    }
    if (typeof global !== 'undefined') {
        return global;
    }
    throw new Error('Unable to locate global object.');
}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const getDefaultsFromGlobal = ()=>getGlobal().__FIREBASE_DEFAULTS__;
/**
 * Attempt to read defaults from a JSON string provided to
 * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in
 * process(.)env(.)__FIREBASE_DEFAULTS_PATH__
 * The dots are in parens because certain compilers (Vite?) cannot
 * handle seeing that variable in comments.
 * See https://github.com/firebase/firebase-js-sdk/issues/6838
 */ const getDefaultsFromEnvVariable = ()=>{
    if (typeof process === 'undefined' || typeof process.env === 'undefined') {
        return;
    }
    const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;
    if (defaultsJsonString) {
        return JSON.parse(defaultsJsonString);
    }
};
const getDefaultsFromCookie = ()=>{
    if (typeof document === 'undefined') {
        return;
    }
    let match;
    try {
        match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);
    } catch (e) {
        // Some environments such as Angular Universal SSR have a
        // `document` object but error on accessing `document.cookie`.
        return;
    }
    const decoded = match && base64Decode(match[1]);
    return decoded && JSON.parse(decoded);
};
/**
 * Get the __FIREBASE_DEFAULTS__ object. It checks in order:
 * (1) if such an object exists as a property of `globalThis`
 * (2) if such an object was provided on a shell environment variable
 * (3) if such an object exists in a cookie
 * @public
 */ const getDefaults = ()=>{
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$postinstall$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultsFromPostinstall"])() || getDefaultsFromGlobal() || getDefaultsFromEnvVariable() || getDefaultsFromCookie();
    } catch (e) {
        /**
         * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due
         * to any environment case we have not accounted for. Log to
         * info instead of swallowing so we can find these unknown cases
         * and add paths for them if needed.
         */ console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);
        return;
    }
};
/**
 * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object
 * for the given product.
 * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available
 * @public
 */ const getDefaultEmulatorHost = (productName)=>{
    var _a, _b;
    return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName];
};
/**
 * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object
 * for the given product.
 * @returns a pair of hostname and port like `["::1", 4000]` if available
 * @public
 */ const getDefaultEmulatorHostnameAndPort = (productName)=>{
    const host = getDefaultEmulatorHost(productName);
    if (!host) {
        return undefined;
    }
    const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.
    if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {
        throw new Error(`Invalid host ${host} with no separate hostname and port!`);
    }
    // eslint-disable-next-line no-restricted-globals
    const port = parseInt(host.substring(separatorIndex + 1), 10);
    if (host[0] === '[') {
        // Bracket-quoted `[ipv6addr]:port` => return "ipv6addr" (without brackets).
        return [
            host.substring(1, separatorIndex - 1),
            port
        ];
    } else {
        return [
            host.substring(0, separatorIndex),
            port
        ];
    }
};
/**
 * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.
 * @public
 */ const getDefaultAppConfig = ()=>{
    var _a;
    return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config;
};
/**
 * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties
 * prefixed by "_")
 * @public
 */ const getExperimentalSetting = (name)=>{
    var _a;
    return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`];
};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ class Deferred {
    constructor(){
        this.reject = ()=>{};
        this.resolve = ()=>{};
        this.promise = new Promise((resolve, reject)=>{
            this.resolve = resolve;
            this.reject = reject;
        });
    }
    /**
     * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around
     * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback
     * and returns a node-style callback which will resolve or reject the Deferred's promise.
     */ wrapCallback(callback) {
        return (error, value)=>{
            if (error) {
                this.reject(error);
            } else {
                this.resolve(value);
            }
            if (typeof callback === 'function') {
                // Attaching noop handler just in case developer wasn't expecting
                // promises
                this.promise.catch(()=>{});
                // Some of our callbacks don't expect a value and our own tests
                // assert that the parameter length is 1
                if (callback.length === 1) {
                    callback(error);
                } else {
                    callback(error, value);
                }
            }
        };
    }
}
/**
 * @license
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Checks whether host is a cloud workstation or not.
 * @public
 */ function isCloudWorkstation(host) {
    return host.endsWith('.cloudworkstations.dev');
}
/**
 * Makes a fetch request to the given server.
 * Mostly used for forwarding cookies in Firebase Studio.
 * @public
 */ async function pingServer(endpoint) {
    const result = await fetch(endpoint, {
        credentials: 'include'
    });
    return result.ok;
}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function createMockUserToken(token, projectId) {
    if (token.uid) {
        throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');
    }
    // Unsecured JWTs use "none" as the algorithm.
    const header = {
        alg: 'none',
        type: 'JWT'
    };
    const project = projectId || 'demo-project';
    const iat = token.iat || 0;
    const sub = token.sub || token.user_id;
    if (!sub) {
        throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");
    }
    const payload = Object.assign({
        // Set all required fields to decent defaults
        iss: `https://securetoken.google.com/${project}`,
        aud: project,
        iat,
        exp: iat + 3600,
        auth_time: iat,
        sub,
        user_id: sub,
        firebase: {
            sign_in_provider: 'custom',
            identities: {}
        }
    }, token);
    // Unsecured JWTs use the empty string as a signature.
    const signature = '';
    return [
        base64urlEncodeWithoutPadding(JSON.stringify(header)),
        base64urlEncodeWithoutPadding(JSON.stringify(payload)),
        signature
    ].join('.');
}
const emulatorStatus = {};
// Checks whether any products are running on an emulator
function getEmulatorSummary() {
    const summary = {
        prod: [],
        emulator: []
    };
    for (const key of Object.keys(emulatorStatus)){
        if (emulatorStatus[key]) {
            summary.emulator.push(key);
        } else {
            summary.prod.push(key);
        }
    }
    return summary;
}
function getOrCreateEl(id) {
    let parentDiv = document.getElementById(id);
    let created = false;
    if (!parentDiv) {
        parentDiv = document.createElement('div');
        parentDiv.setAttribute('id', id);
        created = true;
    }
    return {
        created,
        element: parentDiv
    };
}
let previouslyDismissed = false;
/**
 * Updates Emulator Banner. Primarily used for Firebase Studio
 * @param name
 * @param isRunningEmulator
 * @public
 */ function updateEmulatorBanner(name, isRunningEmulator) {
    if (typeof window === 'undefined' || typeof document === 'undefined' || !isCloudWorkstation(window.location.host) || emulatorStatus[name] === isRunningEmulator || emulatorStatus[name] || // If already set to use emulator, can't go back to prod.
    previouslyDismissed) {
        return;
    }
    emulatorStatus[name] = isRunningEmulator;
    function prefixedId(id) {
        return `__firebase__banner__${id}`;
    }
    const bannerId = '__firebase__banner';
    const summary = getEmulatorSummary();
    const showError = summary.prod.length > 0;
    function tearDown() {
        const element = document.getElementById(bannerId);
        if (element) {
            element.remove();
        }
    }
    function setupBannerStyles(bannerEl) {
        bannerEl.style.display = 'flex';
        bannerEl.style.background = '#7faaf0';
        bannerEl.style.position = 'fixed';
        bannerEl.style.bottom = '5px';
        bannerEl.style.left = '5px';
        bannerEl.style.padding = '.5em';
        bannerEl.style.borderRadius = '5px';
        bannerEl.style.alignItems = 'center';
    }
    function setupIconStyles(prependIcon, iconId) {
        prependIcon.setAttribute('width', '24');
        prependIcon.setAttribute('id', iconId);
        prependIcon.setAttribute('height', '24');
        prependIcon.setAttribute('viewBox', '0 0 24 24');
        prependIcon.setAttribute('fill', 'none');
        prependIcon.style.marginLeft = '-6px';
    }
    function setupCloseBtn() {
        const closeBtn = document.createElement('span');
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.marginLeft = '16px';
        closeBtn.style.fontSize = '24px';
        closeBtn.innerHTML = ' &times;';
        closeBtn.onclick = ()=>{
            previouslyDismissed = true;
            tearDown();
        };
        return closeBtn;
    }
    function setupLinkStyles(learnMoreLink, learnMoreId) {
        learnMoreLink.setAttribute('id', learnMoreId);
        learnMoreLink.innerText = 'Learn more';
        learnMoreLink.href = 'https://firebase.google.com/docs/studio/preview-apps#preview-backend';
        learnMoreLink.setAttribute('target', '__blank');
        learnMoreLink.style.paddingLeft = '5px';
        learnMoreLink.style.textDecoration = 'underline';
    }
    function setupDom() {
        const banner = getOrCreateEl(bannerId);
        const firebaseTextId = prefixedId('text');
        const firebaseText = document.getElementById(firebaseTextId) || document.createElement('span');
        const learnMoreId = prefixedId('learnmore');
        const learnMoreLink = document.getElementById(learnMoreId) || document.createElement('a');
        const prependIconId = prefixedId('preprendIcon');
        const prependIcon = document.getElementById(prependIconId) || document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        if (banner.created) {
            // update styles
            const bannerEl = banner.element;
            setupBannerStyles(bannerEl);
            setupLinkStyles(learnMoreLink, learnMoreId);
            const closeBtn = setupCloseBtn();
            setupIconStyles(prependIcon, prependIconId);
            bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);
            document.body.appendChild(bannerEl);
        }
        if (showError) {
            firebaseText.innerText = `Preview backend disconnected.`;
            prependIcon.innerHTML = `<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`;
        } else {
            prependIcon.innerHTML = `<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`;
            firebaseText.innerText = 'Preview backend running in this workspace.';
        }
        firebaseText.setAttribute('id', firebaseTextId);
    }
    if (document.readyState === 'loading') {
        window.addEventListener('DOMContentLoaded', setupDom);
    } else {
        setupDom();
    }
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Returns navigator.userAgent string or '' if it's not defined.
 * @return user agent string
 */ function getUA() {
    if (typeof navigator !== 'undefined' && typeof navigator['userAgent'] === 'string') {
        return navigator['userAgent'];
    } else {
        return '';
    }
}
/**
 * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.
 *
 * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap
 * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally
 * wait for a callback.
 */ function isMobileCordova() {
    return typeof window !== 'undefined' && // @ts-ignore Setting up an broadly applicable index signature for Window
    // just to deal with this case would probably be a bad idea.
    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) && /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA());
}
/**
 * Detect Node.js.
 *
 * @return true if Node.js environment is detected or specified.
 */ // Node detection logic from: https://github.com/iliakan/detect-node/
function isNode() {
    var _a;
    const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;
    if (forceEnvironment === 'node') {
        return true;
    } else if (forceEnvironment === 'browser') {
        return false;
    }
    try {
        return Object.prototype.toString.call(global.process) === '[object process]';
    } catch (e) {
        return false;
    }
}
/**
 * Detect Browser Environment.
 * Note: This will return true for certain test frameworks that are incompletely
 * mimicking a browser, and should not lead to assuming all browser APIs are
 * available.
 */ function isBrowser() {
    return typeof window !== 'undefined' || isWebWorker();
}
/**
 * Detect Web Worker context.
 */ function isWebWorker() {
    return typeof WorkerGlobalScope !== 'undefined' && typeof self !== 'undefined' && self instanceof WorkerGlobalScope;
}
/**
 * Detect Cloudflare Worker context.
 */ function isCloudflareWorker() {
    return typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers';
}
function isBrowserExtension() {
    const runtime = typeof chrome === 'object' ? chrome.runtime : typeof browser === 'object' ? browser.runtime : undefined;
    return typeof runtime === 'object' && runtime.id !== undefined;
}
/**
 * Detect React Native.
 *
 * @return true if ReactNative environment is detected.
 */ function isReactNative() {
    return typeof navigator === 'object' && navigator['product'] === 'ReactNative';
}
/** Detects Electron apps. */ function isElectron() {
    return getUA().indexOf('Electron/') >= 0;
}
/** Detects Internet Explorer. */ function isIE() {
    const ua = getUA();
    return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;
}
/** Detects Universal Windows Platform apps. */ function isUWP() {
    return getUA().indexOf('MSAppHost/') >= 0;
}
/**
 * Detect whether the current SDK build is the Node version.
 *
 * @return true if it's the Node SDK build.
 */ function isNodeSdk() {
    return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;
}
/** Returns true if we are running in Safari. */ function isSafari() {
    return !isNode() && !!navigator.userAgent && navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');
}
/** Returns true if we are running in Safari or WebKit */ function isSafariOrWebkit() {
    return !isNode() && !!navigator.userAgent && (navigator.userAgent.includes('Safari') || navigator.userAgent.includes('WebKit')) && !navigator.userAgent.includes('Chrome');
}
/**
 * This method checks if indexedDB is supported by current browser/service worker context
 * @return true if indexedDB is supported by current browser/service worker context
 */ function isIndexedDBAvailable() {
    try {
        return typeof indexedDB === 'object';
    } catch (e) {
        return false;
    }
}
/**
 * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject
 * if errors occur during the database open operation.
 *
 * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox
 * private browsing)
 */ function validateIndexedDBOpenable() {
    return new Promise((resolve, reject)=>{
        try {
            let preExist = true;
            const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';
            const request = self.indexedDB.open(DB_CHECK_NAME);
            request.onsuccess = ()=>{
                request.result.close();
                // delete database only when it doesn't pre-exist
                if (!preExist) {
                    self.indexedDB.deleteDatabase(DB_CHECK_NAME);
                }
                resolve(true);
            };
            request.onupgradeneeded = ()=>{
                preExist = false;
            };
            request.onerror = ()=>{
                var _a;
                reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');
            };
        } catch (error) {
            reject(error);
        }
    });
}
/**
 *
 * This method checks whether cookie is enabled within current browser
 * @return true if cookie is enabled within current browser
 */ function areCookiesEnabled() {
    if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {
        return false;
    }
    return true;
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * @fileoverview Standardized Firebase Error.
 *
 * Usage:
 *
 *   // TypeScript string literals for type-safe codes
 *   type Err =
 *     'unknown' |
 *     'object-not-found'
 *     ;
 *
 *   // Closure enum for type-safe error codes
 *   // at-enum {string}
 *   var Err = {
 *     UNKNOWN: 'unknown',
 *     OBJECT_NOT_FOUND: 'object-not-found',
 *   }
 *
 *   let errors: Map<Err, string> = {
 *     'generic-error': "Unknown error",
 *     'file-not-found': "Could not find file: {$file}",
 *   };
 *
 *   // Type-safe function - must pass a valid error code as param.
 *   let error = new ErrorFactory<Err>('service', 'Service', errors);
 *
 *   ...
 *   throw error.create(Err.GENERIC);
 *   ...
 *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});
 *   ...
 *   // Service: Could not file file: foo.txt (service/file-not-found).
 *
 *   catch (e) {
 *     assert(e.message === "Could not find file: foo.txt.");
 *     if ((e as FirebaseError)?.code === 'service/file-not-found') {
 *       console.log("Could not read file: " + e['file']);
 *     }
 *   }
 */ const ERROR_NAME = 'FirebaseError';
// Based on code from:
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types
class FirebaseError extends Error {
    constructor(/** The error code for this error. */ code, message, /** Custom data for this error. */ customData){
        super(message);
        this.code = code;
        this.customData = customData;
        /** The custom name for all FirebaseErrors. */ this.name = ERROR_NAME;
        // Fix For ES5
        // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work
        // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget
        //                   which we can now use since we no longer target ES5.
        Object.setPrototypeOf(this, FirebaseError.prototype);
        // Maintains proper stack trace for where our error was thrown.
        // Only available on V8.
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ErrorFactory.prototype.create);
        }
    }
}
class ErrorFactory {
    constructor(service, serviceName, errors){
        this.service = service;
        this.serviceName = serviceName;
        this.errors = errors;
    }
    create(code, ...data) {
        const customData = data[0] || {};
        const fullCode = `${this.service}/${code}`;
        const template = this.errors[code];
        const message = template ? replaceTemplate(template, customData) : 'Error';
        // Service Name: Error message (service/code).
        const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;
        const error = new FirebaseError(fullCode, fullMessage, customData);
        return error;
    }
}
function replaceTemplate(template, data) {
    return template.replace(PATTERN, (_, key)=>{
        const value = data[key];
        return value != null ? String(value) : `<${key}?>`;
    });
}
const PATTERN = /\{\$([^}]+)}/g;
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Evaluates a JSON string into a javascript object.
 *
 * @param {string} str A string containing JSON.
 * @return {*} The javascript object representing the specified JSON.
 */ function jsonEval(str) {
    return JSON.parse(str);
}
/**
 * Returns JSON representing a javascript object.
 * @param {*} data JavaScript object to be stringified.
 * @return {string} The JSON contents of the object.
 */ function stringify(data) {
    return JSON.stringify(data);
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Decodes a Firebase auth. token into constituent parts.
 *
 * Notes:
 * - May return with invalid / incomplete claims if there's no native base64 decoding support.
 * - Doesn't check if the token is actually valid.
 */ const decode = function(token) {
    let header = {}, claims = {}, data = {}, signature = '';
    try {
        const parts = token.split('.');
        header = jsonEval(base64Decode(parts[0]) || '');
        claims = jsonEval(base64Decode(parts[1]) || '');
        signature = parts[2];
        data = claims['d'] || {};
        delete claims['d'];
    } catch (e) {}
    return {
        header,
        claims,
        data,
        signature
    };
};
/**
 * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the
 * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.
 *
 * Notes:
 * - May return a false negative if there's no native base64 decoding support.
 * - Doesn't check if the token is actually valid.
 */ const isValidTimestamp = function(token) {
    const claims = decode(token).claims;
    const now = Math.floor(new Date().getTime() / 1000);
    let validSince = 0, validUntil = 0;
    if (typeof claims === 'object') {
        if (claims.hasOwnProperty('nbf')) {
            validSince = claims['nbf'];
        } else if (claims.hasOwnProperty('iat')) {
            validSince = claims['iat'];
        }
        if (claims.hasOwnProperty('exp')) {
            validUntil = claims['exp'];
        } else {
            // token will expire after 24h by default
            validUntil = validSince + 86400;
        }
    }
    return !!now && !!validSince && !!validUntil && now >= validSince && now <= validUntil;
};
/**
 * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.
 *
 * Notes:
 * - May return null if there's no native base64 decoding support.
 * - Doesn't check if the token is actually valid.
 */ const issuedAtTime = function(token) {
    const claims = decode(token).claims;
    if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {
        return claims['iat'];
    }
    return null;
};
/**
 * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.
 *
 * Notes:
 * - May return a false negative if there's no native base64 decoding support.
 * - Doesn't check if the token is actually valid.
 */ const isValidFormat = function(token) {
    const decoded = decode(token), claims = decoded.claims;
    return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');
};
/**
 * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.
 *
 * Notes:
 * - May return a false negative if there's no native base64 decoding support.
 * - Doesn't check if the token is actually valid.
 */ const isAdmin = function(token) {
    const claims = decode(token).claims;
    return typeof claims === 'object' && claims['admin'] === true;
};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function contains(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}
function safeGet(obj, key) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
        return obj[key];
    } else {
        return undefined;
    }
}
function isEmpty(obj) {
    for(const key in obj){
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            return false;
        }
    }
    return true;
}
function map(obj, fn, contextObj) {
    const res = {};
    for(const key in obj){
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            res[key] = fn.call(contextObj, obj[key], key, obj);
        }
    }
    return res;
}
/**
 * Deep equal two objects. Support Arrays and Objects.
 */ function deepEqual(a, b) {
    if (a === b) {
        return true;
    }
    const aKeys = Object.keys(a);
    const bKeys = Object.keys(b);
    for (const k of aKeys){
        if (!bKeys.includes(k)) {
            return false;
        }
        const aProp = a[k];
        const bProp = b[k];
        if (isObject(aProp) && isObject(bProp)) {
            if (!deepEqual(aProp, bProp)) {
                return false;
            }
        } else if (aProp !== bProp) {
            return false;
        }
    }
    for (const k of bKeys){
        if (!aKeys.includes(k)) {
            return false;
        }
    }
    return true;
}
function isObject(thing) {
    return thing !== null && typeof thing === 'object';
}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Rejects if the given promise doesn't resolve in timeInMS milliseconds.
 * @internal
 */ function promiseWithTimeout(promise, timeInMS = 2000) {
    const deferredPromise = new Deferred();
    setTimeout(()=>deferredPromise.reject('timeout!'), timeInMS);
    promise.then(deferredPromise.resolve, deferredPromise.reject);
    return deferredPromise.promise;
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a
 * params object (e.g. {arg: 'val', arg2: 'val2'})
 * Note: You must prepend it with ? when adding it to a URL.
 */ function querystring(querystringParams) {
    const params = [];
    for (const [key, value] of Object.entries(querystringParams)){
        if (Array.isArray(value)) {
            value.forEach((arrayVal)=>{
                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));
            });
        } else {
            params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
        }
    }
    return params.length ? '&' + params.join('&') : '';
}
/**
 * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object
 * (e.g. {arg: 'val', arg2: 'val2'})
 */ function querystringDecode(querystring) {
    const obj = {};
    const tokens = querystring.replace(/^\?/, '').split('&');
    tokens.forEach((token)=>{
        if (token) {
            const [key, value] = token.split('=');
            obj[decodeURIComponent(key)] = decodeURIComponent(value);
        }
    });
    return obj;
}
/**
 * Extract the query string part of a URL, including the leading question mark (if present).
 */ function extractQuerystring(url) {
    const queryStart = url.indexOf('?');
    if (!queryStart) {
        return '';
    }
    const fragmentStart = url.indexOf('#', queryStart);
    return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * @fileoverview SHA-1 cryptographic hash.
 * Variable names follow the notation in FIPS PUB 180-3:
 * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.
 *
 * Usage:
 *   var sha1 = new sha1();
 *   sha1.update(bytes);
 *   var hash = sha1.digest();
 *
 * Performance:
 *   Chrome 23:   ~400 Mbit/s
 *   Firefox 16:  ~250 Mbit/s
 *
 */ /**
 * SHA-1 cryptographic hash constructor.
 *
 * The properties declared here are discussed in the above algorithm document.
 * @constructor
 * @final
 * @struct
 */ class Sha1 {
    constructor(){
        /**
         * Holds the previous values of accumulated variables a-e in the compress_
         * function.
         * @private
         */ this.chain_ = [];
        /**
         * A buffer holding the partially computed hash result.
         * @private
         */ this.buf_ = [];
        /**
         * An array of 80 bytes, each a part of the message to be hashed.  Referred to
         * as the message schedule in the docs.
         * @private
         */ this.W_ = [];
        /**
         * Contains data needed to pad messages less than 64 bytes.
         * @private
         */ this.pad_ = [];
        /**
         * @private {number}
         */ this.inbuf_ = 0;
        /**
         * @private {number}
         */ this.total_ = 0;
        this.blockSize = 512 / 8;
        this.pad_[0] = 128;
        for(let i = 1; i < this.blockSize; ++i){
            this.pad_[i] = 0;
        }
        this.reset();
    }
    reset() {
        this.chain_[0] = 0x67452301;
        this.chain_[1] = 0xefcdab89;
        this.chain_[2] = 0x98badcfe;
        this.chain_[3] = 0x10325476;
        this.chain_[4] = 0xc3d2e1f0;
        this.inbuf_ = 0;
        this.total_ = 0;
    }
    /**
     * Internal compress helper function.
     * @param buf Block to compress.
     * @param offset Offset of the block in the buffer.
     * @private
     */ compress_(buf, offset) {
        if (!offset) {
            offset = 0;
        }
        const W = this.W_;
        // get 16 big endian words
        if (typeof buf === 'string') {
            for(let i = 0; i < 16; i++){
                // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS
                // have a bug that turns the post-increment ++ operator into pre-increment
                // during JIT compilation.  We have code that depends heavily on SHA-1 for
                // correctness and which is affected by this bug, so I've removed all uses
                // of post-increment ++ in which the result value is used.  We can revert
                // this change once the Safari bug
                // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and
                // most clients have been updated.
                W[i] = buf.charCodeAt(offset) << 24 | buf.charCodeAt(offset + 1) << 16 | buf.charCodeAt(offset + 2) << 8 | buf.charCodeAt(offset + 3);
                offset += 4;
            }
        } else {
            for(let i = 0; i < 16; i++){
                W[i] = buf[offset] << 24 | buf[offset + 1] << 16 | buf[offset + 2] << 8 | buf[offset + 3];
                offset += 4;
            }
        }
        // expand to 80 words
        for(let i = 16; i < 80; i++){
            const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];
            W[i] = (t << 1 | t >>> 31) & 0xffffffff;
        }
        let a = this.chain_[0];
        let b = this.chain_[1];
        let c = this.chain_[2];
        let d = this.chain_[3];
        let e = this.chain_[4];
        let f, k;
        // TODO(user): Try to unroll this loop to speed up the computation.
        for(let i = 0; i < 80; i++){
            if (i < 40) {
                if (i < 20) {
                    f = d ^ b & (c ^ d);
                    k = 0x5a827999;
                } else {
                    f = b ^ c ^ d;
                    k = 0x6ed9eba1;
                }
            } else {
                if (i < 60) {
                    f = b & c | d & (b | c);
                    k = 0x8f1bbcdc;
                } else {
                    f = b ^ c ^ d;
                    k = 0xca62c1d6;
                }
            }
            const t = (a << 5 | a >>> 27) + f + e + k + W[i] & 0xffffffff;
            e = d;
            d = c;
            c = (b << 30 | b >>> 2) & 0xffffffff;
            b = a;
            a = t;
        }
        this.chain_[0] = this.chain_[0] + a & 0xffffffff;
        this.chain_[1] = this.chain_[1] + b & 0xffffffff;
        this.chain_[2] = this.chain_[2] + c & 0xffffffff;
        this.chain_[3] = this.chain_[3] + d & 0xffffffff;
        this.chain_[4] = this.chain_[4] + e & 0xffffffff;
    }
    update(bytes, length) {
        // TODO(johnlenz): tighten the function signature and remove this check
        if (bytes == null) {
            return;
        }
        if (length === undefined) {
            length = bytes.length;
        }
        const lengthMinusBlock = length - this.blockSize;
        let n = 0;
        // Using local instead of member variables gives ~5% speedup on Firefox 16.
        const buf = this.buf_;
        let inbuf = this.inbuf_;
        // The outer while loop should execute at most twice.
        while(n < length){
            // When we have no data in the block to top up, we can directly process the
            // input buffer (assuming it contains sufficient data). This gives ~25%
            // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that
            // the data is provided in large chunks (or in multiples of 64 bytes).
            if (inbuf === 0) {
                while(n <= lengthMinusBlock){
                    this.compress_(bytes, n);
                    n += this.blockSize;
                }
            }
            if (typeof bytes === 'string') {
                while(n < length){
                    buf[inbuf] = bytes.charCodeAt(n);
                    ++inbuf;
                    ++n;
                    if (inbuf === this.blockSize) {
                        this.compress_(buf);
                        inbuf = 0;
                        break;
                    }
                }
            } else {
                while(n < length){
                    buf[inbuf] = bytes[n];
                    ++inbuf;
                    ++n;
                    if (inbuf === this.blockSize) {
                        this.compress_(buf);
                        inbuf = 0;
                        break;
                    }
                }
            }
        }
        this.inbuf_ = inbuf;
        this.total_ += length;
    }
    /** @override */ digest() {
        const digest = [];
        let totalBits = this.total_ * 8;
        // Add pad 0x80 0x00*.
        if (this.inbuf_ < 56) {
            this.update(this.pad_, 56 - this.inbuf_);
        } else {
            this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));
        }
        // Add # bits.
        for(let i = this.blockSize - 1; i >= 56; i--){
            this.buf_[i] = totalBits & 255;
            totalBits /= 256; // Don't use bit-shifting here!
        }
        this.compress_(this.buf_);
        let n = 0;
        for(let i = 0; i < 5; i++){
            for(let j = 24; j >= 0; j -= 8){
                digest[n] = this.chain_[i] >> j & 255;
                ++n;
            }
        }
        return digest;
    }
}
/**
 * Helper to make a Subscribe function (just like Promise helps make a
 * Thenable).
 *
 * @param executor Function which can make calls to a single Observer
 *     as a proxy.
 * @param onNoObservers Callback when count of Observers goes to zero.
 */ function createSubscribe(executor, onNoObservers) {
    const proxy = new ObserverProxy(executor, onNoObservers);
    return proxy.subscribe.bind(proxy);
}
/**
 * Implement fan-out for any number of Observers attached via a subscribe
 * function.
 */ class ObserverProxy {
    /**
     * @param executor Function which can make calls to a single Observer
     *     as a proxy.
     * @param onNoObservers Callback when count of Observers goes to zero.
     */ constructor(executor, onNoObservers){
        this.observers = [];
        this.unsubscribes = [];
        this.observerCount = 0;
        // Micro-task scheduling by calling task.then().
        this.task = Promise.resolve();
        this.finalized = false;
        this.onNoObservers = onNoObservers;
        // Call the executor asynchronously so subscribers that are called
        // synchronously after the creation of the subscribe function
        // can still receive the very first value generated in the executor.
        this.task.then(()=>{
            executor(this);
        }).catch((e)=>{
            this.error(e);
        });
    }
    next(value) {
        this.forEachObserver((observer)=>{
            observer.next(value);
        });
    }
    error(error) {
        this.forEachObserver((observer)=>{
            observer.error(error);
        });
        this.close(error);
    }
    complete() {
        this.forEachObserver((observer)=>{
            observer.complete();
        });
        this.close();
    }
    /**
     * Subscribe function that can be used to add an Observer to the fan-out list.
     *
     * - We require that no event is sent to a subscriber synchronously to their
     *   call to subscribe().
     */ subscribe(nextOrObserver, error, complete) {
        let observer;
        if (nextOrObserver === undefined && error === undefined && complete === undefined) {
            throw new Error('Missing Observer.');
        }
        // Assemble an Observer object when passed as callback functions.
        if (implementsAnyMethods(nextOrObserver, [
            'next',
            'error',
            'complete'
        ])) {
            observer = nextOrObserver;
        } else {
            observer = {
                next: nextOrObserver,
                error,
                complete
            };
        }
        if (observer.next === undefined) {
            observer.next = noop;
        }
        if (observer.error === undefined) {
            observer.error = noop;
        }
        if (observer.complete === undefined) {
            observer.complete = noop;
        }
        const unsub = this.unsubscribeOne.bind(this, this.observers.length);
        // Attempt to subscribe to a terminated Observable - we
        // just respond to the Observer with the final error or complete
        // event.
        if (this.finalized) {
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.task.then(()=>{
                try {
                    if (this.finalError) {
                        observer.error(this.finalError);
                    } else {
                        observer.complete();
                    }
                } catch (e) {
                // nothing
                }
                return;
            });
        }
        this.observers.push(observer);
        return unsub;
    }
    // Unsubscribe is synchronous - we guarantee that no events are sent to
    // any unsubscribed Observer.
    unsubscribeOne(i) {
        if (this.observers === undefined || this.observers[i] === undefined) {
            return;
        }
        delete this.observers[i];
        this.observerCount -= 1;
        if (this.observerCount === 0 && this.onNoObservers !== undefined) {
            this.onNoObservers(this);
        }
    }
    forEachObserver(fn) {
        if (this.finalized) {
            // Already closed by previous event....just eat the additional values.
            return;
        }
        // Since sendOne calls asynchronously - there is no chance that
        // this.observers will become undefined.
        for(let i = 0; i < this.observers.length; i++){
            this.sendOne(i, fn);
        }
    }
    // Call the Observer via one of it's callback function. We are careful to
    // confirm that the observe has not been unsubscribed since this asynchronous
    // function had been queued.
    sendOne(i, fn) {
        // Execute the callback asynchronously
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this.task.then(()=>{
            if (this.observers !== undefined && this.observers[i] !== undefined) {
                try {
                    fn(this.observers[i]);
                } catch (e) {
                    // Ignore exceptions raised in Observers or missing methods of an
                    // Observer.
                    // Log error to console. b/31404806
                    if (typeof console !== 'undefined' && console.error) {
                        console.error(e);
                    }
                }
            }
        });
    }
    close(err) {
        if (this.finalized) {
            return;
        }
        this.finalized = true;
        if (err !== undefined) {
            this.finalError = err;
        }
        // Proxy is no longer needed - garbage collect references
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this.task.then(()=>{
            this.observers = undefined;
            this.onNoObservers = undefined;
        });
    }
}
/** Turn synchronous function into one called asynchronously. */ // eslint-disable-next-line @typescript-eslint/ban-types
function async(fn, onError) {
    return (...args)=>{
        Promise.resolve(true).then(()=>{
            fn(...args);
        }).catch((error)=>{
            if (onError) {
                onError(error);
            }
        });
    };
}
/**
 * Return true if the object passed in implements any of the named methods.
 */ function implementsAnyMethods(obj, methods) {
    if (typeof obj !== 'object' || obj === null) {
        return false;
    }
    for (const method of methods){
        if (method in obj && typeof obj[method] === 'function') {
            return true;
        }
    }
    return false;
}
function noop() {
// do nothing
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Check to make sure the appropriate number of arguments are provided for a public function.
 * Throws an error if it fails.
 *
 * @param fnName The function name
 * @param minCount The minimum number of arguments to allow for the function call
 * @param maxCount The maximum number of argument to allow for the function call
 * @param argCount The actual number of arguments provided.
 */ const validateArgCount = function(fnName, minCount, maxCount, argCount) {
    let argError;
    if (argCount < minCount) {
        argError = 'at least ' + minCount;
    } else if (argCount > maxCount) {
        argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;
    }
    if (argError) {
        const error = fnName + ' failed: Was called with ' + argCount + (argCount === 1 ? ' argument.' : ' arguments.') + ' Expects ' + argError + '.';
        throw new Error(error);
    }
};
/**
 * Generates a string to prefix an error message about failed argument validation
 *
 * @param fnName The function name
 * @param argName The name of the argument
 * @return The prefix to add to the error thrown for validation.
 */ function errorPrefix(fnName, argName) {
    return `${fnName} failed: ${argName} argument `;
}
/**
 * @param fnName
 * @param argumentNumber
 * @param namespace
 * @param optional
 */ function validateNamespace(fnName, namespace, optional) {
    if (optional && !namespace) {
        return;
    }
    if (typeof namespace !== 'string') {
        //TODO: I should do more validation here. We only allow certain chars in namespaces.
        throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');
    }
}
function validateCallback(fnName, argumentName, // eslint-disable-next-line @typescript-eslint/ban-types
callback, optional) {
    if (optional && !callback) {
        return;
    }
    if (typeof callback !== 'function') {
        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');
    }
}
function validateContextObject(fnName, argumentName, context, optional) {
    if (optional && !context) {
        return;
    }
    if (typeof context !== 'object' || context === null) {
        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');
    }
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they
// automatically replaced '\r\n' with '\n', and they didn't handle surrogate pairs,
// so it's been modified.
// Note that not all Unicode characters appear as single characters in JavaScript strings.
// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters
// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first
// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate
// pair).
// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3
/**
 * @param {string} str
 * @return {Array}
 */ const stringToByteArray = function(str) {
    const out = [];
    let p = 0;
    for(let i = 0; i < str.length; i++){
        let c = str.charCodeAt(i);
        // Is this the lead surrogate in a surrogate pair?
        if (c >= 0xd800 && c <= 0xdbff) {
            const high = c - 0xd800; // the high 10 bits.
            i++;
            assert(i < str.length, 'Surrogate pair missing trail surrogate.');
            const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.
            c = 0x10000 + (high << 10) + low;
        }
        if (c < 128) {
            out[p++] = c;
        } else if (c < 2048) {
            out[p++] = c >> 6 | 192;
            out[p++] = c & 63 | 128;
        } else if (c < 65536) {
            out[p++] = c >> 12 | 224;
            out[p++] = c >> 6 & 63 | 128;
            out[p++] = c & 63 | 128;
        } else {
            out[p++] = c >> 18 | 240;
            out[p++] = c >> 12 & 63 | 128;
            out[p++] = c >> 6 & 63 | 128;
            out[p++] = c & 63 | 128;
        }
    }
    return out;
};
/**
 * Calculate length without actually converting; useful for doing cheaper validation.
 * @param {string} str
 * @return {number}
 */ const stringLength = function(str) {
    let p = 0;
    for(let i = 0; i < str.length; i++){
        const c = str.charCodeAt(i);
        if (c < 128) {
            p++;
        } else if (c < 2048) {
            p += 2;
        } else if (c >= 0xd800 && c <= 0xdbff) {
            // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.
            p += 4;
            i++; // skip trail surrogate.
        } else {
            p += 3;
        }
    }
    return p;
};
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * The amount of milliseconds to exponentially increase.
 */ const DEFAULT_INTERVAL_MILLIS = 1000;
/**
 * The factor to backoff by.
 * Should be a number greater than 1.
 */ const DEFAULT_BACKOFF_FACTOR = 2;
/**
 * The maximum milliseconds to increase to.
 *
 * <p>Visible for testing
 */ const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.
/**
 * The percentage of backoff time to randomize by.
 * See
 * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic
 * for context.
 *
 * <p>Visible for testing
 */ const RANDOM_FACTOR = 0.5;
/**
 * Based on the backoff method from
 * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.
 * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.
 */ function calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {
    // Calculates an exponentially increasing value.
    // Deviation: calculates value from count and a constant interval, so we only need to save value
    // and count to restore state.
    const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);
    // A random "fuzz" to avoid waves of retries.
    // Deviation: randomFactor is required.
    const randomWait = Math.round(// A fraction of the backoff value to add/subtract.
    // Deviation: changes multiplication order to improve readability.
    RANDOM_FACTOR * currBaseValue * // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines
    // if we add or subtract.
    (Math.random() - 0.5) * 2);
    // Limits backoff to max to avoid effectively permanent backoff.
    return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);
}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Provide English ordinal letters after a number
 */ function ordinal(i) {
    if (!Number.isFinite(i)) {
        return `${i}`;
    }
    return i + indicator(i);
}
function indicator(i) {
    i = Math.abs(i);
    const cent = i % 100;
    if (cent >= 10 && cent <= 20) {
        return 'th';
    }
    const dec = i % 10;
    if (dec === 1) {
        return 'st';
    }
    if (dec === 2) {
        return 'nd';
    }
    if (dec === 3) {
        return 'rd';
    }
    return 'th';
}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function getModularInstance(service) {
    if (service && service._delegate) {
        return service._delegate;
    } else {
        return service;
    }
}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Overriding the constant (we should be the only ones doing this)
CONSTANTS.NODE_CLIENT = true;
;
 //# sourceMappingURL=index.node.esm.js.map
}}),
"[project]/node_modules/@firebase/logger/dist/esm/index.esm2017.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * A container for all of the Logger instances
 */ __turbopack_context__.s({
    "LogLevel": (()=>LogLevel),
    "Logger": (()=>Logger),
    "setLogLevel": (()=>setLogLevel),
    "setUserLogHandler": (()=>setUserLogHandler)
});
const instances = [];
/**
 * The JS SDK supports 5 log levels and also allows a user the ability to
 * silence the logs altogether.
 *
 * The order is a follows:
 * DEBUG < VERBOSE < INFO < WARN < ERROR
 *
 * All of the log types above the current log level will be captured (i.e. if
 * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and
 * `VERBOSE` logs will not)
 */ var LogLevel;
(function(LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["VERBOSE"] = 1] = "VERBOSE";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["WARN"] = 3] = "WARN";
    LogLevel[LogLevel["ERROR"] = 4] = "ERROR";
    LogLevel[LogLevel["SILENT"] = 5] = "SILENT";
})(LogLevel || (LogLevel = {}));
const levelStringToEnum = {
    'debug': LogLevel.DEBUG,
    'verbose': LogLevel.VERBOSE,
    'info': LogLevel.INFO,
    'warn': LogLevel.WARN,
    'error': LogLevel.ERROR,
    'silent': LogLevel.SILENT
};
/**
 * The default log level
 */ const defaultLogLevel = LogLevel.INFO;
/**
 * By default, `console.debug` is not displayed in the developer console (in
 * chrome). To avoid forcing users to have to opt-in to these logs twice
 * (i.e. once for firebase, and once in the console), we are sending `DEBUG`
 * logs to the `console.log` function.
 */ const ConsoleMethod = {
    [LogLevel.DEBUG]: 'log',
    [LogLevel.VERBOSE]: 'log',
    [LogLevel.INFO]: 'info',
    [LogLevel.WARN]: 'warn',
    [LogLevel.ERROR]: 'error'
};
/**
 * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR
 * messages on to their corresponding console counterparts (if the log method
 * is supported by the current log level)
 */ const defaultLogHandler = (instance, logType, ...args)=>{
    if (logType < instance.logLevel) {
        return;
    }
    const now = new Date().toISOString();
    const method = ConsoleMethod[logType];
    if (method) {
        console[method](`[${now}]  ${instance.name}:`, ...args);
    } else {
        throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);
    }
};
class Logger {
    /**
     * Gives you an instance of a Logger to capture messages according to
     * Firebase's logging scheme.
     *
     * @param name The name that the logs will be associated with
     */ constructor(name){
        this.name = name;
        /**
         * The log level of the given Logger instance.
         */ this._logLevel = defaultLogLevel;
        /**
         * The main (internal) log handler for the Logger instance.
         * Can be set to a new function in internal package code but not by user.
         */ this._logHandler = defaultLogHandler;
        /**
         * The optional, additional, user-defined log handler for the Logger instance.
         */ this._userLogHandler = null;
        /**
         * Capture the current instance for later use
         */ instances.push(this);
    }
    get logLevel() {
        return this._logLevel;
    }
    set logLevel(val) {
        if (!(val in LogLevel)) {
            throw new TypeError(`Invalid value "${val}" assigned to \`logLevel\``);
        }
        this._logLevel = val;
    }
    // Workaround for setter/getter having to be the same type.
    setLogLevel(val) {
        this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;
    }
    get logHandler() {
        return this._logHandler;
    }
    set logHandler(val) {
        if (typeof val !== 'function') {
            throw new TypeError('Value assigned to `logHandler` must be a function');
        }
        this._logHandler = val;
    }
    get userLogHandler() {
        return this._userLogHandler;
    }
    set userLogHandler(val) {
        this._userLogHandler = val;
    }
    /**
     * The functions below are all based on the `console` interface
     */ debug(...args) {
        this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);
        this._logHandler(this, LogLevel.DEBUG, ...args);
    }
    log(...args) {
        this._userLogHandler && this._userLogHandler(this, LogLevel.VERBOSE, ...args);
        this._logHandler(this, LogLevel.VERBOSE, ...args);
    }
    info(...args) {
        this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);
        this._logHandler(this, LogLevel.INFO, ...args);
    }
    warn(...args) {
        this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);
        this._logHandler(this, LogLevel.WARN, ...args);
    }
    error(...args) {
        this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);
        this._logHandler(this, LogLevel.ERROR, ...args);
    }
}
function setLogLevel(level) {
    instances.forEach((inst)=>{
        inst.setLogLevel(level);
    });
}
function setUserLogHandler(logCallback, options) {
    for (const instance of instances){
        let customLogLevel = null;
        if (options && options.level) {
            customLogLevel = levelStringToEnum[options.level];
        }
        if (logCallback === null) {
            instance.userLogHandler = null;
        } else {
            instance.userLogHandler = (instance, level, ...args)=>{
                const message = args.map((arg)=>{
                    if (arg == null) {
                        return null;
                    } else if (typeof arg === 'string') {
                        return arg;
                    } else if (typeof arg === 'number' || typeof arg === 'boolean') {
                        return arg.toString();
                    } else if (arg instanceof Error) {
                        return arg.message;
                    } else {
                        try {
                            return JSON.stringify(arg);
                        } catch (ignored) {
                            return null;
                        }
                    }
                }).filter((arg)=>arg).join(' ');
                if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {
                    logCallback({
                        level: LogLevel[level].toLowerCase(),
                        message,
                        args,
                        type: instance.name
                    });
                }
            };
        }
    }
}
;
 //# sourceMappingURL=index.esm2017.js.map
}}),
"[project]/node_modules/@firebase/component/dist/esm/index.esm2017.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Component": (()=>Component),
    "ComponentContainer": (()=>ComponentContainer),
    "Provider": (()=>Provider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/util/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)");
;
/**
 * Component for service name T, e.g. `auth`, `auth-internal`
 */ class Component {
    /**
     *
     * @param name The public service name, e.g. app, auth, firestore, database
     * @param instanceFactory Service factory responsible for creating the public interface
     * @param type whether the service provided by the component is public or private
     */ constructor(name, instanceFactory, type){
        this.name = name;
        this.instanceFactory = instanceFactory;
        this.type = type;
        this.multipleInstances = false;
        /**
         * Properties to be added to the service namespace
         */ this.serviceProps = {};
        this.instantiationMode = "LAZY" /* InstantiationMode.LAZY */ ;
        this.onInstanceCreated = null;
    }
    setInstantiationMode(mode) {
        this.instantiationMode = mode;
        return this;
    }
    setMultipleInstances(multipleInstances) {
        this.multipleInstances = multipleInstances;
        return this;
    }
    setServiceProps(props) {
        this.serviceProps = props;
        return this;
    }
    setInstanceCreatedCallback(callback) {
        this.onInstanceCreated = callback;
        return this;
    }
}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const DEFAULT_ENTRY_NAME = '[DEFAULT]';
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Provider for instance for service name T, e.g. 'auth', 'auth-internal'
 * NameServiceMapping[T] is an alias for the type of the instance
 */ class Provider {
    constructor(name, container){
        this.name = name;
        this.container = container;
        this.component = null;
        this.instances = new Map();
        this.instancesDeferred = new Map();
        this.instancesOptions = new Map();
        this.onInitCallbacks = new Map();
    }
    /**
     * @param identifier A provider can provide multiple instances of a service
     * if this.component.multipleInstances is true.
     */ get(identifier) {
        // if multipleInstances is not supported, use the default name
        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);
        if (!this.instancesDeferred.has(normalizedIdentifier)) {
            const deferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"]();
            this.instancesDeferred.set(normalizedIdentifier, deferred);
            if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {
                // initialize the service if it can be auto-initialized
                try {
                    const instance = this.getOrInitializeService({
                        instanceIdentifier: normalizedIdentifier
                    });
                    if (instance) {
                        deferred.resolve(instance);
                    }
                } catch (e) {
                // when the instance factory throws an exception during get(), it should not cause
                // a fatal error. We just return the unresolved promise in this case.
                }
            }
        }
        return this.instancesDeferred.get(normalizedIdentifier).promise;
    }
    getImmediate(options) {
        var _a;
        // if multipleInstances is not supported, use the default name
        const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);
        const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;
        if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {
            try {
                return this.getOrInitializeService({
                    instanceIdentifier: normalizedIdentifier
                });
            } catch (e) {
                if (optional) {
                    return null;
                } else {
                    throw e;
                }
            }
        } else {
            // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw
            if (optional) {
                return null;
            } else {
                throw Error(`Service ${this.name} is not available`);
            }
        }
    }
    getComponent() {
        return this.component;
    }
    setComponent(component) {
        if (component.name !== this.name) {
            throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);
        }
        if (this.component) {
            throw Error(`Component for ${this.name} has already been provided`);
        }
        this.component = component;
        // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)
        if (!this.shouldAutoInitialize()) {
            return;
        }
        // if the service is eager, initialize the default instance
        if (isComponentEager(component)) {
            try {
                this.getOrInitializeService({
                    instanceIdentifier: DEFAULT_ENTRY_NAME
                });
            } catch (e) {
            // when the instance factory for an eager Component throws an exception during the eager
            // initialization, it should not cause a fatal error.
            // TODO: Investigate if we need to make it configurable, because some component may want to cause
            // a fatal error in this case?
            }
        }
        // Create service instances for the pending promises and resolve them
        // NOTE: if this.multipleInstances is false, only the default instance will be created
        // and all promises with resolve with it regardless of the identifier.
        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()){
            const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);
            try {
                // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.
                const instance = this.getOrInitializeService({
                    instanceIdentifier: normalizedIdentifier
                });
                instanceDeferred.resolve(instance);
            } catch (e) {
            // when the instance factory throws an exception, it should not cause
            // a fatal error. We just leave the promise unresolved.
            }
        }
    }
    clearInstance(identifier = DEFAULT_ENTRY_NAME) {
        this.instancesDeferred.delete(identifier);
        this.instancesOptions.delete(identifier);
        this.instances.delete(identifier);
    }
    // app.delete() will call this method on every provider to delete the services
    // TODO: should we mark the provider as deleted?
    async delete() {
        const services = Array.from(this.instances.values());
        await Promise.all([
            ...services.filter((service)=>'INTERNAL' in service) // legacy services
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .map((service)=>service.INTERNAL.delete()),
            ...services.filter((service)=>'_delete' in service) // modularized services
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .map((service)=>service._delete())
        ]);
    }
    isComponentSet() {
        return this.component != null;
    }
    isInitialized(identifier = DEFAULT_ENTRY_NAME) {
        return this.instances.has(identifier);
    }
    getOptions(identifier = DEFAULT_ENTRY_NAME) {
        return this.instancesOptions.get(identifier) || {};
    }
    initialize(opts = {}) {
        const { options = {} } = opts;
        const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);
        if (this.isInitialized(normalizedIdentifier)) {
            throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);
        }
        if (!this.isComponentSet()) {
            throw Error(`Component ${this.name} has not been registered yet`);
        }
        const instance = this.getOrInitializeService({
            instanceIdentifier: normalizedIdentifier,
            options
        });
        // resolve any pending promise waiting for the service instance
        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()){
            const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);
            if (normalizedIdentifier === normalizedDeferredIdentifier) {
                instanceDeferred.resolve(instance);
            }
        }
        return instance;
    }
    /**
     *
     * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().
     * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.
     *
     * @param identifier An optional instance identifier
     * @returns a function to unregister the callback
     */ onInit(callback, identifier) {
        var _a;
        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);
        const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();
        existingCallbacks.add(callback);
        this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);
        const existingInstance = this.instances.get(normalizedIdentifier);
        if (existingInstance) {
            callback(existingInstance, normalizedIdentifier);
        }
        return ()=>{
            existingCallbacks.delete(callback);
        };
    }
    /**
     * Invoke onInit callbacks synchronously
     * @param instance the service instance`
     */ invokeOnInitCallbacks(instance, identifier) {
        const callbacks = this.onInitCallbacks.get(identifier);
        if (!callbacks) {
            return;
        }
        for (const callback of callbacks){
            try {
                callback(instance, identifier);
            } catch (_a) {
            // ignore errors in the onInit callback
            }
        }
    }
    getOrInitializeService({ instanceIdentifier, options = {} }) {
        let instance = this.instances.get(instanceIdentifier);
        if (!instance && this.component) {
            instance = this.component.instanceFactory(this.container, {
                instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),
                options
            });
            this.instances.set(instanceIdentifier, instance);
            this.instancesOptions.set(instanceIdentifier, options);
            /**
             * Invoke onInit listeners.
             * Note this.component.onInstanceCreated is different, which is used by the component creator,
             * while onInit listeners are registered by consumers of the provider.
             */ this.invokeOnInitCallbacks(instance, instanceIdentifier);
            /**
             * Order is important
             * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which
             * makes `isInitialized()` return true.
             */ if (this.component.onInstanceCreated) {
                try {
                    this.component.onInstanceCreated(this.container, instanceIdentifier, instance);
                } catch (_a) {
                // ignore errors in the onInstanceCreatedCallback
                }
            }
        }
        return instance || null;
    }
    normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {
        if (this.component) {
            return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;
        } else {
            return identifier; // assume multiple instances are supported before the component is provided.
        }
    }
    shouldAutoInitialize() {
        return !!this.component && this.component.instantiationMode !== "EXPLICIT" /* InstantiationMode.EXPLICIT */ ;
    }
}
// undefined should be passed to the service factory for the default instance
function normalizeIdentifierForFactory(identifier) {
    return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;
}
function isComponentEager(component) {
    return component.instantiationMode === "EAGER" /* InstantiationMode.EAGER */ ;
}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`
 */ class ComponentContainer {
    constructor(name){
        this.name = name;
        this.providers = new Map();
    }
    /**
     *
     * @param component Component being added
     * @param overwrite When a component with the same name has already been registered,
     * if overwrite is true: overwrite the existing component with the new component and create a new
     * provider with the new component. It can be useful in tests where you want to use different mocks
     * for different tests.
     * if overwrite is false: throw an exception
     */ addComponent(component) {
        const provider = this.getProvider(component.name);
        if (provider.isComponentSet()) {
            throw new Error(`Component ${component.name} has already been registered with ${this.name}`);
        }
        provider.setComponent(component);
    }
    addOrOverwriteComponent(component) {
        const provider = this.getProvider(component.name);
        if (provider.isComponentSet()) {
            // delete the existing provider from the container, so we can register the new component
            this.providers.delete(component.name);
        }
        this.addComponent(component);
    }
    /**
     * getProvider provides a type safe interface where it can only be called with a field name
     * present in NameServiceMapping interface.
     *
     * Firebase SDKs providing services should extend NameServiceMapping interface to register
     * themselves.
     */ getProvider(name) {
        if (this.providers.has(name)) {
            return this.providers.get(name);
        }
        // create a Provider for a service that hasn't registered with Firebase
        const provider = new Provider(name, this);
        this.providers.set(name, provider);
        return provider;
    }
    getProviders() {
        return Array.from(this.providers.values());
    }
}
;
 //# sourceMappingURL=index.esm2017.js.map
}}),
"[project]/node_modules/idb/build/wrap-idb-value.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "a": (()=>reverseTransformCache),
    "i": (()=>instanceOfAny),
    "r": (()=>replaceTraps),
    "u": (()=>unwrap),
    "w": (()=>wrap)
});
const instanceOfAny = (object, constructors)=>constructors.some((c)=>object instanceof c);
let idbProxyableTypes;
let cursorAdvanceMethods;
// This is a function to prevent it throwing up in node environments.
function getIdbProxyableTypes() {
    return idbProxyableTypes || (idbProxyableTypes = [
        IDBDatabase,
        IDBObjectStore,
        IDBIndex,
        IDBCursor,
        IDBTransaction
    ]);
}
// This is a function to prevent it throwing up in node environments.
function getCursorAdvanceMethods() {
    return cursorAdvanceMethods || (cursorAdvanceMethods = [
        IDBCursor.prototype.advance,
        IDBCursor.prototype.continue,
        IDBCursor.prototype.continuePrimaryKey
    ]);
}
const cursorRequestMap = new WeakMap();
const transactionDoneMap = new WeakMap();
const transactionStoreNamesMap = new WeakMap();
const transformCache = new WeakMap();
const reverseTransformCache = new WeakMap();
function promisifyRequest(request) {
    const promise = new Promise((resolve, reject)=>{
        const unlisten = ()=>{
            request.removeEventListener('success', success);
            request.removeEventListener('error', error);
        };
        const success = ()=>{
            resolve(wrap(request.result));
            unlisten();
        };
        const error = ()=>{
            reject(request.error);
            unlisten();
        };
        request.addEventListener('success', success);
        request.addEventListener('error', error);
    });
    promise.then((value)=>{
        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval
        // (see wrapFunction).
        if (value instanceof IDBCursor) {
            cursorRequestMap.set(value, request);
        }
    // Catching to avoid "Uncaught Promise exceptions"
    }).catch(()=>{});
    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This
    // is because we create many promises from a single IDBRequest.
    reverseTransformCache.set(promise, request);
    return promise;
}
function cacheDonePromiseForTransaction(tx) {
    // Early bail if we've already created a done promise for this transaction.
    if (transactionDoneMap.has(tx)) return;
    const done = new Promise((resolve, reject)=>{
        const unlisten = ()=>{
            tx.removeEventListener('complete', complete);
            tx.removeEventListener('error', error);
            tx.removeEventListener('abort', error);
        };
        const complete = ()=>{
            resolve();
            unlisten();
        };
        const error = ()=>{
            reject(tx.error || new DOMException('AbortError', 'AbortError'));
            unlisten();
        };
        tx.addEventListener('complete', complete);
        tx.addEventListener('error', error);
        tx.addEventListener('abort', error);
    });
    // Cache it for later retrieval.
    transactionDoneMap.set(tx, done);
}
let idbProxyTraps = {
    get (target, prop, receiver) {
        if (target instanceof IDBTransaction) {
            // Special handling for transaction.done.
            if (prop === 'done') return transactionDoneMap.get(target);
            // Polyfill for objectStoreNames because of Edge.
            if (prop === 'objectStoreNames') {
                return target.objectStoreNames || transactionStoreNamesMap.get(target);
            }
            // Make tx.store return the only store in the transaction, or undefined if there are many.
            if (prop === 'store') {
                return receiver.objectStoreNames[1] ? undefined : receiver.objectStore(receiver.objectStoreNames[0]);
            }
        }
        // Else transform whatever we get back.
        return wrap(target[prop]);
    },
    set (target, prop, value) {
        target[prop] = value;
        return true;
    },
    has (target, prop) {
        if (target instanceof IDBTransaction && (prop === 'done' || prop === 'store')) {
            return true;
        }
        return prop in target;
    }
};
function replaceTraps(callback) {
    idbProxyTraps = callback(idbProxyTraps);
}
function wrapFunction(func) {
    // Due to expected object equality (which is enforced by the caching in `wrap`), we
    // only create one new func per func.
    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.
    if (func === IDBDatabase.prototype.transaction && !('objectStoreNames' in IDBTransaction.prototype)) {
        return function(storeNames, ...args) {
            const tx = func.call(unwrap(this), storeNames, ...args);
            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [
                storeNames
            ]);
            return wrap(tx);
        };
    }
    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In
    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the
    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense
    // with real promises, so each advance methods returns a new promise for the cursor object, or
    // undefined if the end of the cursor has been reached.
    if (getCursorAdvanceMethods().includes(func)) {
        return function(...args) {
            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use
            // the original object.
            func.apply(unwrap(this), args);
            return wrap(cursorRequestMap.get(this));
        };
    }
    return function(...args) {
        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use
        // the original object.
        return wrap(func.apply(unwrap(this), args));
    };
}
function transformCachableValue(value) {
    if (typeof value === 'function') return wrapFunction(value);
    // This doesn't return, it just creates a 'done' promise for the transaction,
    // which is later returned for transaction.done (see idbObjectHandler).
    if (value instanceof IDBTransaction) cacheDonePromiseForTransaction(value);
    if (instanceOfAny(value, getIdbProxyableTypes())) return new Proxy(value, idbProxyTraps);
    // Return the same value back if we're not going to transform it.
    return value;
}
function wrap(value) {
    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because
    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.
    if (value instanceof IDBRequest) return promisifyRequest(value);
    // If we've already transformed this value before, reuse the transformed value.
    // This is faster, but it also provides object equality.
    if (transformCache.has(value)) return transformCache.get(value);
    const newValue = transformCachableValue(value);
    // Not all types are transformed.
    // These may be primitive types, so they can't be WeakMap keys.
    if (newValue !== value) {
        transformCache.set(value, newValue);
        reverseTransformCache.set(newValue, value);
    }
    return newValue;
}
const unwrap = (value)=>reverseTransformCache.get(value);
;
}}),
"[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteDB": (()=>deleteDB),
    "openDB": (()=>openDB)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/idb/build/wrap-idb-value.js [app-ssr] (ecmascript)");
;
;
/**
 * Open a database.
 *
 * @param name Name of the database.
 * @param version Schema version.
 * @param callbacks Additional callbacks.
 */ function openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {
    const request = indexedDB.open(name, version);
    const openPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["w"])(request);
    if (upgrade) {
        request.addEventListener('upgradeneeded', (event)=>{
            upgrade((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["w"])(request.result), event.oldVersion, event.newVersion, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["w"])(request.transaction), event);
        });
    }
    if (blocked) {
        request.addEventListener('blocked', (event)=>blocked(// Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405
            event.oldVersion, event.newVersion, event));
    }
    openPromise.then((db)=>{
        if (terminated) db.addEventListener('close', ()=>terminated());
        if (blocking) {
            db.addEventListener('versionchange', (event)=>blocking(event.oldVersion, event.newVersion, event));
        }
    }).catch(()=>{});
    return openPromise;
}
/**
 * Delete a database.
 *
 * @param name Name of the database.
 */ function deleteDB(name, { blocked } = {}) {
    const request = indexedDB.deleteDatabase(name);
    if (blocked) {
        request.addEventListener('blocked', (event)=>blocked(// Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405
            event.oldVersion, event));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["w"])(request).then(()=>undefined);
}
const readMethods = [
    'get',
    'getKey',
    'getAll',
    'getAllKeys',
    'count'
];
const writeMethods = [
    'put',
    'add',
    'delete',
    'clear'
];
const cachedMethods = new Map();
function getMethod(target, prop) {
    if (!(target instanceof IDBDatabase && !(prop in target) && typeof prop === 'string')) {
        return;
    }
    if (cachedMethods.get(prop)) return cachedMethods.get(prop);
    const targetFuncName = prop.replace(/FromIndex$/, '');
    const useIndex = prop !== targetFuncName;
    const isWrite = writeMethods.includes(targetFuncName);
    if (// Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.
    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) || !(isWrite || readMethods.includes(targetFuncName))) {
        return;
    }
    const method = async function(storeName, ...args) {
        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(
        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');
        let target = tx.store;
        if (useIndex) target = target.index(args.shift());
        // Must reject if op rejects.
        // If it's a write operation, must reject if tx.done rejects.
        // Must reject with op rejection first.
        // Must resolve with op value.
        // Must handle both promises (no unhandled rejections)
        return (await Promise.all([
            target[targetFuncName](...args),
            isWrite && tx.done
        ]))[0];
    };
    cachedMethods.set(prop, method);
    return method;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["r"])((oldTraps)=>({
        ...oldTraps,
        get: (target, prop, receiver)=>getMethod(target, prop) || oldTraps.get(target, prop, receiver),
        has: (target, prop)=>!!getMethod(target, prop) || oldTraps.has(target, prop)
    }));
;
}}),
"[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$wrap$2d$idb$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/idb/build/wrap-idb-value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@firebase/app/dist/esm/index.esm2017.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SDK_VERSION": (()=>SDK_VERSION),
    "_DEFAULT_ENTRY_NAME": (()=>DEFAULT_ENTRY_NAME),
    "_addComponent": (()=>_addComponent),
    "_addOrOverwriteComponent": (()=>_addOrOverwriteComponent),
    "_apps": (()=>_apps),
    "_clearComponents": (()=>_clearComponents),
    "_components": (()=>_components),
    "_getProvider": (()=>_getProvider),
    "_isFirebaseApp": (()=>_isFirebaseApp),
    "_isFirebaseServerApp": (()=>_isFirebaseServerApp),
    "_registerComponent": (()=>_registerComponent),
    "_removeServiceInstance": (()=>_removeServiceInstance),
    "_serverApps": (()=>_serverApps),
    "deleteApp": (()=>deleteApp),
    "getApp": (()=>getApp),
    "getApps": (()=>getApps),
    "initializeApp": (()=>initializeApp),
    "initializeServerApp": (()=>initializeServerApp),
    "onLog": (()=>onLog),
    "registerVersion": (()=>registerVersion),
    "setLogLevel": (()=>setLogLevel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/component/dist/esm/index.esm2017.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$logger$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/logger/dist/esm/index.esm2017.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/util/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ class PlatformLoggerServiceImpl {
    constructor(container){
        this.container = container;
    }
    // In initial implementation, this will be called by installations on
    // auth token refresh, and installations will send this string.
    getPlatformInfoString() {
        const providers = this.container.getProviders();
        // Loop through providers and get library/version pairs from any that are
        // version components.
        return providers.map((provider)=>{
            if (isVersionServiceProvider(provider)) {
                const service = provider.getImmediate();
                return `${service.library}/${service.version}`;
            } else {
                return null;
            }
        }).filter((logString)=>logString).join(' ');
    }
}
/**
 *
 * @param provider check if this provider provides a VersionService
 *
 * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider
 * provides VersionService. The provider is not necessarily a 'app-version'
 * provider.
 */ function isVersionServiceProvider(provider) {
    const component = provider.getComponent();
    return (component === null || component === void 0 ? void 0 : component.type) === "VERSION" /* ComponentType.VERSION */ ;
}
const name$q = "@firebase/app";
const version$1 = "0.13.0";
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const logger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$logger$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Logger"]('@firebase/app');
const name$p = "@firebase/app-compat";
const name$o = "@firebase/analytics-compat";
const name$n = "@firebase/analytics";
const name$m = "@firebase/app-check-compat";
const name$l = "@firebase/app-check";
const name$k = "@firebase/auth";
const name$j = "@firebase/auth-compat";
const name$i = "@firebase/database";
const name$h = "@firebase/data-connect";
const name$g = "@firebase/database-compat";
const name$f = "@firebase/functions";
const name$e = "@firebase/functions-compat";
const name$d = "@firebase/installations";
const name$c = "@firebase/installations-compat";
const name$b = "@firebase/messaging";
const name$a = "@firebase/messaging-compat";
const name$9 = "@firebase/performance";
const name$8 = "@firebase/performance-compat";
const name$7 = "@firebase/remote-config";
const name$6 = "@firebase/remote-config-compat";
const name$5 = "@firebase/storage";
const name$4 = "@firebase/storage-compat";
const name$3 = "@firebase/firestore";
const name$2 = "@firebase/ai";
const name$1 = "@firebase/firestore-compat";
const name = "firebase";
const version = "11.8.0";
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * The default app name
 *
 * @internal
 */ const DEFAULT_ENTRY_NAME = '[DEFAULT]';
const PLATFORM_LOG_STRING = {
    [name$q]: 'fire-core',
    [name$p]: 'fire-core-compat',
    [name$n]: 'fire-analytics',
    [name$o]: 'fire-analytics-compat',
    [name$l]: 'fire-app-check',
    [name$m]: 'fire-app-check-compat',
    [name$k]: 'fire-auth',
    [name$j]: 'fire-auth-compat',
    [name$i]: 'fire-rtdb',
    [name$h]: 'fire-data-connect',
    [name$g]: 'fire-rtdb-compat',
    [name$f]: 'fire-fn',
    [name$e]: 'fire-fn-compat',
    [name$d]: 'fire-iid',
    [name$c]: 'fire-iid-compat',
    [name$b]: 'fire-fcm',
    [name$a]: 'fire-fcm-compat',
    [name$9]: 'fire-perf',
    [name$8]: 'fire-perf-compat',
    [name$7]: 'fire-rc',
    [name$6]: 'fire-rc-compat',
    [name$5]: 'fire-gcs',
    [name$4]: 'fire-gcs-compat',
    [name$3]: 'fire-fst',
    [name$1]: 'fire-fst-compat',
    [name$2]: 'fire-vertex',
    'fire-js': 'fire-js',
    [name]: 'fire-js-all'
};
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * @internal
 */ const _apps = new Map();
/**
 * @internal
 */ const _serverApps = new Map();
/**
 * Registered components.
 *
 * @internal
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const _components = new Map();
/**
 * @param component - the component being added to this app's container
 *
 * @internal
 */ function _addComponent(app, component) {
    try {
        app.container.addComponent(component);
    } catch (e) {
        logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);
    }
}
/**
 *
 * @internal
 */ function _addOrOverwriteComponent(app, component) {
    app.container.addOrOverwriteComponent(component);
}
/**
 *
 * @param component - the component to register
 * @returns whether or not the component is registered successfully
 *
 * @internal
 */ function _registerComponent(component) {
    const componentName = component.name;
    if (_components.has(componentName)) {
        logger.debug(`There were multiple attempts to register component ${componentName}.`);
        return false;
    }
    _components.set(componentName, component);
    // add the component to existing app instances
    for (const app of _apps.values()){
        _addComponent(app, component);
    }
    for (const serverApp of _serverApps.values()){
        _addComponent(serverApp, component);
    }
    return true;
}
/**
 *
 * @param app - FirebaseApp instance
 * @param name - service name
 *
 * @returns the provider for the service with the matching name
 *
 * @internal
 */ function _getProvider(app, name) {
    const heartbeatController = app.container.getProvider('heartbeat').getImmediate({
        optional: true
    });
    if (heartbeatController) {
        void heartbeatController.triggerHeartbeat();
    }
    return app.container.getProvider(name);
}
/**
 *
 * @param app - FirebaseApp instance
 * @param name - service name
 * @param instanceIdentifier - service instance identifier in case the service supports multiple instances
 *
 * @internal
 */ function _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {
    _getProvider(app, name).clearInstance(instanceIdentifier);
}
/**
 *
 * @param obj - an object of type FirebaseApp or FirebaseOptions.
 *
 * @returns true if the provide object is of type FirebaseApp.
 *
 * @internal
 */ function _isFirebaseApp(obj) {
    return obj.options !== undefined;
}
/**
 *
 * @param obj - an object of type FirebaseApp.
 *
 * @returns true if the provided object is of type FirebaseServerAppImpl.
 *
 * @internal
 */ function _isFirebaseServerApp(obj) {
    if (obj === null || obj === undefined) {
        return false;
    }
    return obj.settings !== undefined;
}
/**
 * Test only
 *
 * @internal
 */ function _clearComponents() {
    _components.clear();
}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const ERRORS = {
    ["no-app" /* AppError.NO_APP */ ]: "No Firebase App '{$appName}' has been created - " + 'call initializeApp() first',
    ["bad-app-name" /* AppError.BAD_APP_NAME */ ]: "Illegal App name: '{$appName}'",
    ["duplicate-app" /* AppError.DUPLICATE_APP */ ]: "Firebase App named '{$appName}' already exists with different options or config",
    ["app-deleted" /* AppError.APP_DELETED */ ]: "Firebase App named '{$appName}' already deleted",
    ["server-app-deleted" /* AppError.SERVER_APP_DELETED */ ]: 'Firebase Server App has been deleted',
    ["no-options" /* AppError.NO_OPTIONS */ ]: 'Need to provide options, when not being deployed to hosting via source.',
    ["invalid-app-argument" /* AppError.INVALID_APP_ARGUMENT */ ]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',
    ["invalid-log-argument" /* AppError.INVALID_LOG_ARGUMENT */ ]: 'First argument to `onLog` must be null or a function.',
    ["idb-open" /* AppError.IDB_OPEN */ ]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',
    ["idb-get" /* AppError.IDB_GET */ ]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',
    ["idb-set" /* AppError.IDB_WRITE */ ]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',
    ["idb-delete" /* AppError.IDB_DELETE */ ]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',
    ["finalization-registry-not-supported" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */ ]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',
    ["invalid-server-app-environment" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */ ]: 'FirebaseServerApp is not for use in browser environments.'
};
const ERROR_FACTORY = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorFactory"]('app', 'Firebase', ERRORS);
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ class FirebaseAppImpl {
    constructor(options, config, container){
        this._isDeleted = false;
        this._options = Object.assign({}, options);
        this._config = Object.assign({}, config);
        this._name = config.name;
        this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;
        this._container = container;
        this.container.addComponent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Component"]('app', ()=>this, "PUBLIC" /* ComponentType.PUBLIC */ ));
    }
    get automaticDataCollectionEnabled() {
        this.checkDestroyed();
        return this._automaticDataCollectionEnabled;
    }
    set automaticDataCollectionEnabled(val) {
        this.checkDestroyed();
        this._automaticDataCollectionEnabled = val;
    }
    get name() {
        this.checkDestroyed();
        return this._name;
    }
    get options() {
        this.checkDestroyed();
        return this._options;
    }
    get config() {
        this.checkDestroyed();
        return this._config;
    }
    get container() {
        return this._container;
    }
    get isDeleted() {
        return this._isDeleted;
    }
    set isDeleted(val) {
        this._isDeleted = val;
    }
    /**
     * This function will throw an Error if the App has already been deleted -
     * use before performing API actions on the App.
     */ checkDestroyed() {
        if (this.isDeleted) {
            throw ERROR_FACTORY.create("app-deleted" /* AppError.APP_DELETED */ , {
                appName: this._name
            });
        }
    }
}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Parse the token and check to see if the `exp` claim is in the future.
// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in
// the past.
function validateTokenTTL(base64Token, tokenName) {
    const secondPart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["base64Decode"])(base64Token.split('.')[1]);
    if (secondPart === null) {
        console.error(`FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`);
        return;
    }
    const expClaim = JSON.parse(secondPart).exp;
    if (expClaim === undefined) {
        console.error(`FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`);
        return;
    }
    const exp = JSON.parse(secondPart).exp * 1000;
    const now = new Date().getTime();
    const diff = exp - now;
    if (diff <= 0) {
        console.error(`FirebaseServerApp ${tokenName} is invalid: the token has expired.`);
    }
}
class FirebaseServerAppImpl extends FirebaseAppImpl {
    constructor(options, serverConfig, name, container){
        // Build configuration parameters for the FirebaseAppImpl base class.
        const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined ? serverConfig.automaticDataCollectionEnabled : true;
        // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.
        const config = {
            name,
            automaticDataCollectionEnabled
        };
        if (options.apiKey !== undefined) {
            // Construct the parent FirebaseAppImp object.
            super(options, config, container);
        } else {
            const appImpl = options;
            super(appImpl.options, config, container);
        }
        // Now construct the data for the FirebaseServerAppImpl.
        this._serverConfig = Object.assign({
            automaticDataCollectionEnabled
        }, serverConfig);
        // Ensure that the current time is within the `authIdtoken` window of validity.
        if (this._serverConfig.authIdToken) {
            validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');
        }
        // Ensure that the current time is within the `appCheckToken` window of validity.
        if (this._serverConfig.appCheckToken) {
            validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');
        }
        this._finalizationRegistry = null;
        if (typeof FinalizationRegistry !== 'undefined') {
            this._finalizationRegistry = new FinalizationRegistry(()=>{
                this.automaticCleanup();
            });
        }
        this._refCount = 0;
        this.incRefCount(this._serverConfig.releaseOnDeref);
        // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry
        // will never trigger.
        this._serverConfig.releaseOnDeref = undefined;
        serverConfig.releaseOnDeref = undefined;
        registerVersion(name$q, version$1, 'serverapp');
    }
    toJSON() {
        return undefined;
    }
    get refCount() {
        return this._refCount;
    }
    // Increment the reference count of this server app. If an object is provided, register it
    // with the finalization registry.
    incRefCount(obj) {
        if (this.isDeleted) {
            return;
        }
        this._refCount++;
        if (obj !== undefined && this._finalizationRegistry !== null) {
            this._finalizationRegistry.register(obj, this);
        }
    }
    // Decrement the reference count.
    decRefCount() {
        if (this.isDeleted) {
            return 0;
        }
        return --this._refCount;
    }
    // Invoked by the FinalizationRegistry callback to note that this app should go through its
    // reference counts and delete itself if no reference count remain. The coordinating logic that
    // handles this is in deleteApp(...).
    automaticCleanup() {
        void deleteApp(this);
    }
    get settings() {
        this.checkDestroyed();
        return this._serverConfig;
    }
    /**
     * This function will throw an Error if the App has already been deleted -
     * use before performing API actions on the App.
     */ checkDestroyed() {
        if (this.isDeleted) {
            throw ERROR_FACTORY.create("server-app-deleted" /* AppError.SERVER_APP_DELETED */ );
        }
    }
}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * The current SDK version.
 *
 * @public
 */ const SDK_VERSION = version;
function initializeApp(_options, rawConfig = {}) {
    let options = _options;
    if (typeof rawConfig !== 'object') {
        const name = rawConfig;
        rawConfig = {
            name
        };
    }
    const config = Object.assign({
        name: DEFAULT_ENTRY_NAME,
        automaticDataCollectionEnabled: true
    }, rawConfig);
    const name = config.name;
    if (typeof name !== 'string' || !name) {
        throw ERROR_FACTORY.create("bad-app-name" /* AppError.BAD_APP_NAME */ , {
            appName: String(name)
        });
    }
    options || (options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultAppConfig"])());
    if (!options) {
        throw ERROR_FACTORY.create("no-options" /* AppError.NO_OPTIONS */ );
    }
    const existingApp = _apps.get(name);
    if (existingApp) {
        // return the existing app if options and config deep equal the ones in the existing app.
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deepEqual"])(options, existingApp.options) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deepEqual"])(config, existingApp.config)) {
            return existingApp;
        } else {
            throw ERROR_FACTORY.create("duplicate-app" /* AppError.DUPLICATE_APP */ , {
                appName: name
            });
        }
    }
    const container = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ComponentContainer"](name);
    for (const component of _components.values()){
        container.addComponent(component);
    }
    const newApp = new FirebaseAppImpl(options, config, container);
    _apps.set(name, newApp);
    return newApp;
}
function initializeServerApp(_options, _serverAppConfig) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBrowser"])() && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isWebWorker"])()) {
        // FirebaseServerApp isn't designed to be run in browsers.
        throw ERROR_FACTORY.create("invalid-server-app-environment" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */ );
    }
    if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {
        _serverAppConfig.automaticDataCollectionEnabled = true;
    }
    let appOptions;
    if (_isFirebaseApp(_options)) {
        appOptions = _options.options;
    } else {
        appOptions = _options;
    }
    // Build an app name based on a hash of the configuration options.
    const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);
    // However, Do not mangle the name based on releaseOnDeref, since it will vary between the
    // construction of FirebaseServerApp instances. For example, if the object is the request headers.
    if (nameObj.releaseOnDeref !== undefined) {
        delete nameObj.releaseOnDeref;
    }
    const hashCode = (s)=>{
        return [
            ...s
        ].reduce((hash, c)=>Math.imul(31, hash) + c.charCodeAt(0) | 0, 0);
    };
    if (_serverAppConfig.releaseOnDeref !== undefined) {
        if (typeof FinalizationRegistry === 'undefined') {
            throw ERROR_FACTORY.create("finalization-registry-not-supported" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */ , {});
        }
    }
    const nameString = '' + hashCode(JSON.stringify(nameObj));
    const existingApp = _serverApps.get(nameString);
    if (existingApp) {
        existingApp.incRefCount(_serverAppConfig.releaseOnDeref);
        return existingApp;
    }
    const container = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ComponentContainer"](nameString);
    for (const component of _components.values()){
        container.addComponent(component);
    }
    const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);
    _serverApps.set(nameString, newApp);
    return newApp;
}
/**
 * Retrieves a {@link @firebase/app#FirebaseApp} instance.
 *
 * When called with no arguments, the default app is returned. When an app name
 * is provided, the app corresponding to that name is returned.
 *
 * An exception is thrown if the app being retrieved has not yet been
 * initialized.
 *
 * @example
 * ```javascript
 * // Return the default app
 * const app = getApp();
 * ```
 *
 * @example
 * ```javascript
 * // Return a named app
 * const otherApp = getApp("otherApp");
 * ```
 *
 * @param name - Optional name of the app to return. If no name is
 *   provided, the default is `"[DEFAULT]"`.
 *
 * @returns The app corresponding to the provided app name.
 *   If no app name is provided, the default app is returned.
 *
 * @public
 */ function getApp(name = DEFAULT_ENTRY_NAME) {
    const app = _apps.get(name);
    if (!app && name === DEFAULT_ENTRY_NAME && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultAppConfig"])()) {
        return initializeApp();
    }
    if (!app) {
        throw ERROR_FACTORY.create("no-app" /* AppError.NO_APP */ , {
            appName: name
        });
    }
    return app;
}
/**
 * A (read-only) array of all initialized apps.
 * @public
 */ function getApps() {
    return Array.from(_apps.values());
}
/**
 * Renders this app unusable and frees the resources of all associated
 * services.
 *
 * @example
 * ```javascript
 * deleteApp(app)
 *   .then(function() {
 *     console.log("App deleted successfully");
 *   })
 *   .catch(function(error) {
 *     console.log("Error deleting app:", error);
 *   });
 * ```
 *
 * @public
 */ async function deleteApp(app) {
    let cleanupProviders = false;
    const name = app.name;
    if (_apps.has(name)) {
        cleanupProviders = true;
        _apps.delete(name);
    } else if (_serverApps.has(name)) {
        const firebaseServerApp = app;
        if (firebaseServerApp.decRefCount() <= 0) {
            _serverApps.delete(name);
            cleanupProviders = true;
        }
    }
    if (cleanupProviders) {
        await Promise.all(app.container.getProviders().map((provider)=>provider.delete()));
        app.isDeleted = true;
    }
}
/**
 * Registers a library's name and version for platform logging purposes.
 * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)
 * @param version - Current version of that library.
 * @param variant - Bundle variant, e.g., node, rn, etc.
 *
 * @public
 */ function registerVersion(libraryKeyOrName, version, variant) {
    var _a;
    // TODO: We can use this check to whitelist strings when/if we set up
    // a good whitelist system.
    let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;
    if (variant) {
        library += `-${variant}`;
    }
    const libraryMismatch = library.match(/\s|\//);
    const versionMismatch = version.match(/\s|\//);
    if (libraryMismatch || versionMismatch) {
        const warning = [
            `Unable to register library "${library}" with version "${version}":`
        ];
        if (libraryMismatch) {
            warning.push(`library name "${library}" contains illegal characters (whitespace or "/")`);
        }
        if (libraryMismatch && versionMismatch) {
            warning.push('and');
        }
        if (versionMismatch) {
            warning.push(`version name "${version}" contains illegal characters (whitespace or "/")`);
        }
        logger.warn(warning.join(' '));
        return;
    }
    _registerComponent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Component"](`${library}-version`, ()=>({
            library,
            version
        }), "VERSION" /* ComponentType.VERSION */ ));
}
/**
 * Sets log handler for all Firebase SDKs.
 * @param logCallback - An optional custom log handler that executes user code whenever
 * the Firebase SDK makes a logging call.
 *
 * @public
 */ function onLog(logCallback, options) {
    if (logCallback !== null && typeof logCallback !== 'function') {
        throw ERROR_FACTORY.create("invalid-log-argument" /* AppError.INVALID_LOG_ARGUMENT */ );
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$logger$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setUserLogHandler"])(logCallback, options);
}
/**
 * Sets log level for all Firebase SDKs.
 *
 * All of the log types above the current log level are captured (i.e. if
 * you set the log level to `info`, errors are logged, but `debug` and
 * `verbose` logs are not).
 *
 * @public
 */ function setLogLevel(logLevel) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$logger$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setLogLevel"])(logLevel);
}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const DB_NAME = 'firebase-heartbeat-database';
const DB_VERSION = 1;
const STORE_NAME = 'firebase-heartbeat-store';
let dbPromise = null;
function getDbPromise() {
    if (!dbPromise) {
        dbPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["openDB"])(DB_NAME, DB_VERSION, {
            upgrade: (db, oldVersion)=>{
                // We don't use 'break' in this switch statement, the fall-through
                // behavior is what we want, because if there are multiple versions between
                // the old version and the current version, we want ALL the migrations
                // that correspond to those versions to run, not only the last one.
                // eslint-disable-next-line default-case
                switch(oldVersion){
                    case 0:
                        try {
                            db.createObjectStore(STORE_NAME);
                        } catch (e) {
                            // Safari/iOS browsers throw occasional exceptions on
                            // db.createObjectStore() that may be a bug. Avoid blocking
                            // the rest of the app functionality.
                            console.warn(e);
                        }
                }
            }
        }).catch((e)=>{
            throw ERROR_FACTORY.create("idb-open" /* AppError.IDB_OPEN */ , {
                originalErrorMessage: e.message
            });
        });
    }
    return dbPromise;
}
async function readHeartbeatsFromIndexedDB(app) {
    try {
        const db = await getDbPromise();
        const tx = db.transaction(STORE_NAME);
        const result = await tx.objectStore(STORE_NAME).get(computeKey(app));
        // We already have the value but tx.done can throw,
        // so we need to await it here to catch errors
        await tx.done;
        return result;
    } catch (e) {
        if (e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FirebaseError"]) {
            logger.warn(e.message);
        } else {
            const idbGetError = ERROR_FACTORY.create("idb-get" /* AppError.IDB_GET */ , {
                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message
            });
            logger.warn(idbGetError.message);
        }
    }
}
async function writeHeartbeatsToIndexedDB(app, heartbeatObject) {
    try {
        const db = await getDbPromise();
        const tx = db.transaction(STORE_NAME, 'readwrite');
        const objectStore = tx.objectStore(STORE_NAME);
        await objectStore.put(heartbeatObject, computeKey(app));
        await tx.done;
    } catch (e) {
        if (e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FirebaseError"]) {
            logger.warn(e.message);
        } else {
            const idbGetError = ERROR_FACTORY.create("idb-set" /* AppError.IDB_WRITE */ , {
                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message
            });
            logger.warn(idbGetError.message);
        }
    }
}
function computeKey(app) {
    return `${app.name}!${app.options.appId}`;
}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const MAX_HEADER_BYTES = 1024;
const MAX_NUM_STORED_HEARTBEATS = 30;
class HeartbeatServiceImpl {
    constructor(container){
        this.container = container;
        /**
         * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate
         * the header string.
         * Stores one record per date. This will be consolidated into the standard
         * format of one record per user agent string before being sent as a header.
         * Populated from indexedDB when the controller is instantiated and should
         * be kept in sync with indexedDB.
         * Leave public for easier testing.
         */ this._heartbeatsCache = null;
        const app = this.container.getProvider('app').getImmediate();
        this._storage = new HeartbeatStorageImpl(app);
        this._heartbeatsCachePromise = this._storage.read().then((result)=>{
            this._heartbeatsCache = result;
            return result;
        });
    }
    /**
     * Called to report a heartbeat. The function will generate
     * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it
     * to IndexedDB.
     * Note that we only store one heartbeat per day. So if a heartbeat for today is
     * already logged, subsequent calls to this function in the same day will be ignored.
     */ async triggerHeartbeat() {
        var _a, _b;
        try {
            const platformLogger = this.container.getProvider('platform-logger').getImmediate();
            // This is the "Firebase user agent" string from the platform logger
            // service, not the browser user agent.
            const agent = platformLogger.getPlatformInfoString();
            const date = getUTCDateString();
            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {
                this._heartbeatsCache = await this._heartbeatsCachePromise;
                // If we failed to construct a heartbeats cache, then return immediately.
                if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {
                    return;
                }
            }
            // Do not store a heartbeat if one is already stored for this day
            // or if a header has already been sent today.
            if (this._heartbeatsCache.lastSentHeartbeatDate === date || this._heartbeatsCache.heartbeats.some((singleDateHeartbeat)=>singleDateHeartbeat.date === date)) {
                return;
            } else {
                // There is no entry for this date. Create one.
                this._heartbeatsCache.heartbeats.push({
                    date,
                    agent
                });
                // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.
                // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.
                if (this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS) {
                    const earliestHeartbeatIdx = getEarliestHeartbeatIdx(this._heartbeatsCache.heartbeats);
                    this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);
                }
            }
            return this._storage.overwrite(this._heartbeatsCache);
        } catch (e) {
            logger.warn(e);
        }
    }
    /**
     * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.
     * It also clears all heartbeats from memory as well as in IndexedDB.
     *
     * NOTE: Consuming product SDKs should not send the header if this method
     * returns an empty string.
     */ async getHeartbeatsHeader() {
        var _a;
        try {
            if (this._heartbeatsCache === null) {
                await this._heartbeatsCachePromise;
            }
            // If it's still null or the array is empty, there is no data to send.
            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null || this._heartbeatsCache.heartbeats.length === 0) {
                return '';
            }
            const date = getUTCDateString();
            // Extract as many heartbeats from the cache as will fit under the size limit.
            const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);
            const headerString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["base64urlEncodeWithoutPadding"])(JSON.stringify({
                version: 2,
                heartbeats: heartbeatsToSend
            }));
            // Store last sent date to prevent another being logged/sent for the same day.
            this._heartbeatsCache.lastSentHeartbeatDate = date;
            if (unsentEntries.length > 0) {
                // Store any unsent entries if they exist.
                this._heartbeatsCache.heartbeats = unsentEntries;
                // This seems more likely than emptying the array (below) to lead to some odd state
                // since the cache isn't empty and this will be called again on the next request,
                // and is probably safest if we await it.
                await this._storage.overwrite(this._heartbeatsCache);
            } else {
                this._heartbeatsCache.heartbeats = [];
                // Do not wait for this, to reduce latency.
                void this._storage.overwrite(this._heartbeatsCache);
            }
            return headerString;
        } catch (e) {
            logger.warn(e);
            return '';
        }
    }
}
function getUTCDateString() {
    const today = new Date();
    // Returns date format 'YYYY-MM-DD'
    return today.toISOString().substring(0, 10);
}
function extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {
    // Heartbeats grouped by user agent in the standard format to be sent in
    // the header.
    const heartbeatsToSend = [];
    // Single date format heartbeats that are not sent.
    let unsentEntries = heartbeatsCache.slice();
    for (const singleDateHeartbeat of heartbeatsCache){
        // Look for an existing entry with the same user agent.
        const heartbeatEntry = heartbeatsToSend.find((hb)=>hb.agent === singleDateHeartbeat.agent);
        if (!heartbeatEntry) {
            // If no entry for this user agent exists, create one.
            heartbeatsToSend.push({
                agent: singleDateHeartbeat.agent,
                dates: [
                    singleDateHeartbeat.date
                ]
            });
            if (countBytes(heartbeatsToSend) > maxSize) {
                // If the header would exceed max size, remove the added heartbeat
                // entry and stop adding to the header.
                heartbeatsToSend.pop();
                break;
            }
        } else {
            heartbeatEntry.dates.push(singleDateHeartbeat.date);
            // If the header would exceed max size, remove the added date
            // and stop adding to the header.
            if (countBytes(heartbeatsToSend) > maxSize) {
                heartbeatEntry.dates.pop();
                break;
            }
        }
        // Pop unsent entry from queue. (Skipped if adding the entry exceeded
        // quota and the loop breaks early.)
        unsentEntries = unsentEntries.slice(1);
    }
    return {
        heartbeatsToSend,
        unsentEntries
    };
}
class HeartbeatStorageImpl {
    constructor(app){
        this.app = app;
        this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();
    }
    async runIndexedDBEnvironmentCheck() {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isIndexedDBAvailable"])()) {
            return false;
        } else {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateIndexedDBOpenable"])().then(()=>true).catch(()=>false);
        }
    }
    /**
     * Read all heartbeats.
     */ async read() {
        const canUseIndexedDB = await this._canUseIndexedDBPromise;
        if (!canUseIndexedDB) {
            return {
                heartbeats: []
            };
        } else {
            const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);
            if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {
                return idbHeartbeatObject;
            } else {
                return {
                    heartbeats: []
                };
            }
        }
    }
    // overwrite the storage with the provided heartbeats
    async overwrite(heartbeatsObject) {
        var _a;
        const canUseIndexedDB = await this._canUseIndexedDBPromise;
        if (!canUseIndexedDB) {
            return;
        } else {
            const existingHeartbeatsObject = await this.read();
            return writeHeartbeatsToIndexedDB(this.app, {
                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,
                heartbeats: heartbeatsObject.heartbeats
            });
        }
    }
    // add heartbeats
    async add(heartbeatsObject) {
        var _a;
        const canUseIndexedDB = await this._canUseIndexedDBPromise;
        if (!canUseIndexedDB) {
            return;
        } else {
            const existingHeartbeatsObject = await this.read();
            return writeHeartbeatsToIndexedDB(this.app, {
                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,
                heartbeats: [
                    ...existingHeartbeatsObject.heartbeats,
                    ...heartbeatsObject.heartbeats
                ]
            });
        }
    }
}
/**
 * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped
 * in a platform logging header JSON object, stringified, and converted
 * to base 64.
 */ function countBytes(heartbeatsCache) {
    // base64 has a restricted set of characters, all of which should be 1 byte.
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["base64urlEncodeWithoutPadding"])(// heartbeatsCache wrapper properties
    JSON.stringify({
        version: 2,
        heartbeats: heartbeatsCache
    })).length;
}
/**
 * Returns the index of the heartbeat with the earliest date.
 * If the heartbeats array is empty, -1 is returned.
 */ function getEarliestHeartbeatIdx(heartbeats) {
    if (heartbeats.length === 0) {
        return -1;
    }
    let earliestHeartbeatIdx = 0;
    let earliestHeartbeatDate = heartbeats[0].date;
    for(let i = 1; i < heartbeats.length; i++){
        if (heartbeats[i].date < earliestHeartbeatDate) {
            earliestHeartbeatDate = heartbeats[i].date;
            earliestHeartbeatIdx = i;
        }
    }
    return earliestHeartbeatIdx;
}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function registerCoreComponents(variant) {
    _registerComponent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Component"]('platform-logger', (container)=>new PlatformLoggerServiceImpl(container), "PRIVATE" /* ComponentType.PRIVATE */ ));
    _registerComponent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Component"]('heartbeat', (container)=>new HeartbeatServiceImpl(container), "PRIVATE" /* ComponentType.PRIVATE */ ));
    // Register `app` package.
    registerVersion(name$q, version$1, variant);
    // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation
    registerVersion(name$q, version$1, 'esm2017');
    // Register platform SDK identifier (no version).
    registerVersion('fire-js', '');
}
/**
 * Firebase App
 *
 * @remarks This package coordinates the communication between the different Firebase components
 * @packageDocumentation
 */ registerCoreComponents('');
;
 //# sourceMappingURL=index.esm2017.js.map
}}),
"[project]/node_modules/@firebase/app/dist/esm/index.esm2017.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$component$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/component/dist/esm/index.esm2017.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$logger$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/logger/dist/esm/index.esm2017.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$util$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/util/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2f$build$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/idb/build/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$app$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/app/dist/esm/index.esm2017.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/firebase/database/dist/index.mjs [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$database$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/database/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)"); //# sourceMappingURL=index.mjs.map
;
}}),
"[project]/node_modules/firebase/database/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$database$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/database/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$database$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/database/dist/index.mjs [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) return obj;
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") return {
        default: obj
    };
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) return cache.get(obj);
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);
            else newObj[key] = obj[key];
        }
    }
    newObj.default = obj;
    if (cache) cache.set(obj, newObj);
    return newObj;
}
exports._ = _interop_require_wildcard;
}}),
"[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_base.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _class_private_field_loose_base(receiver, privateKey) {
    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {
        throw new TypeError("attempted to use private field on non-instance");
    }
    return receiver;
}
exports._ = _class_private_field_loose_base;
}}),
"[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_key.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var id = 0;
function _class_private_field_loose_key(name) {
    return "__private_" + id++ + "_" + name;
}
exports._ = _class_private_field_loose_key;
}}),
"[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
exports._ = _interop_require_default;
}}),
"[project]/node_modules/@swc/helpers/cjs/_tagged_template_literal_loose.cjs [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _tagged_template_literal_loose(strings, raw) {
    if (!raw) raw = strings.slice(0);
    strings.raw = raw;
    return strings;
}
exports._ = _tagged_template_literal_loose;
}}),

};

//# sourceMappingURL=node_modules_bd298eae._.js.map