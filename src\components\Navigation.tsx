'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { FiMenu, FiX, FiSearch, FiShoppingBag, FiHeart, FiUser } from 'react-icons/fi';

interface NavigationProps {
  onSearch?: (query: string) => void;
  onCategoryFilter?: (category: string) => void;
}

export default function Navigation({ onSearch, onCategoryFilter }: NavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery);
    }
  };

  const categories = [
    { name: 'iPhone', icon: '📱' },
    { name: 'Samsung', icon: '📲' },
    { name: 'Google', icon: '🔍' },
    { name: 'OnePlus', icon: '⚡' },
    { name: 'Deals', icon: '🔥' }
  ];

  return (
    <>
      {/* Top Bar */}
      <div className="bg-gradient-black-gold text-white py-2 text-center text-sm">
        <p>🚚 Free delivery in Nairobi • 📞 Call us: +254 700 000 000</p>
      </div>

      {/* Main Navigation */}
      <nav className="glass sticky top-0 z-50 border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 hover-scale">
              <div className="relative">
                <Image
                  src="/logo.svg"
                  alt="High End Phones.ke"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-gold rounded-full pulse-gold"></div>
              </div>
              <div>
                <span className="text-2xl font-bold text-gradient block">
                  High End Phones
                </span>
                <span className="text-xs text-gray-600 font-medium">.ke</span>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              <Link
                href="/"
                className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group"
              >
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full"></span>
              </Link>

              {/* Categories Dropdown */}
              <div className="relative group">
                <button className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium flex items-center space-x-1">
                  <span>Categories</span>
                  <svg className="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Dropdown Menu */}
                <div className="absolute top-full left-0 mt-2 w-48 glass rounded-xl shadow-soft opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <div className="p-2">
                    {categories.map((category) => (
                      <button
                        key={category.name}
                        onClick={() => onCategoryFilter?.(category.name)}
                        className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 rounded-lg transition-all duration-200"
                      >
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium">{category.name}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <a
                href="#about"
                className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group"
              >
                About
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a
                href="#contact"
                className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group"
              >
                Contact
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full"></span>
              </a>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="hidden md:flex items-center">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search phones, brands..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-80 pl-12 pr-4 py-3 glass rounded-full focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 placeholder-gray-500"
                />
                <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg" />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 gradient-gold text-black p-2 rounded-full hover-glow transition-all duration-300"
                >
                  <FiSearch size={16} />
                </button>
              </div>
            </form>

            {/* Action Icons */}
            <div className="hidden md:flex items-center space-x-4">
              <button className="p-3 glass rounded-full hover-glow transition-all duration-300 relative">
                <FiHeart size={20} />
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">2</span>
              </button>
              <button className="p-3 glass rounded-full hover-glow transition-all duration-300 relative">
                <FiShoppingBag size={20} />
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-gold text-black text-xs rounded-full flex items-center justify-center">0</span>
              </button>
              <button className="p-3 glass rounded-full hover-glow transition-all duration-300">
                <FiUser size={20} />
              </button>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-3 glass rounded-full hover-glow transition-all duration-300"
            >
              {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden">
              <div className="px-2 pt-2 pb-6 space-y-1 glass-dark rounded-b-xl mt-2">
                {/* Mobile Search */}
                <form onSubmit={handleSearch} className="px-3 py-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search phones..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 glass rounded-full focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                    />
                    <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </form>

                {/* Mobile Menu Items */}
                <Link
                  href="/"
                  className="block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  🏠 Home
                </Link>

                {/* Mobile Categories */}
                <div className="px-6 py-2">
                  <p className="text-gray-400 text-sm font-medium mb-2">Categories</p>
                  <div className="grid grid-cols-2 gap-2">
                    {categories.map((category) => (
                      <button
                        key={category.name}
                        onClick={() => {
                          onCategoryFilter?.(category.name);
                          setIsMenuOpen(false);
                        }}
                        className="flex items-center space-x-2 px-4 py-2 text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                      >
                        <span>{category.icon}</span>
                        <span className="text-sm">{category.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                <a
                  href="#about"
                  className="block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  ℹ️ About
                </a>
                <a
                  href="#contact"
                  className="block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  📞 Contact
                </a>
              </div>
            </div>
          )}
        </div>
      </nav>
    </>
  );
}
