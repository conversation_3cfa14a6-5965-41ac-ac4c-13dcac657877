# High End Phones.ke

A modern, responsive e-commerce website for premium smartphones in Kenya. Built with Next.js 15, TypeScript, Tailwind CSS, and Firebase.

## 🌟 Features

- **Modern Design**: Clean, professional design with golden yellow and black theme
- **Responsive Layout**: Optimized for all devices (mobile, tablet, desktop)
- **Hero Carousel**: Featured phones with auto-advancing slides
- **Search Functionality**: Real-time phone search and filtering
- **Phone Catalog**: Grid layout showcasing all available phones
- **Phone Details**: Dedicated pages for each phone with images/videos
- **WhatsApp Integration**: Direct purchase via WhatsApp
- **Firebase Integration**: Real-time database for phone inventory
- **Cloudinary Support**: Optimized image and video delivery
- **SEO Optimized**: Meta tags and structured data

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: Firebase Realtime Database
- **Media**: Cloudinary
- **Icons**: React Icons (Feather Icons)
- **Deployment**: Vercel-ready

## 📱 Pages

1. **Home Page** (`/`)
   - Navigation with search
   - Hero carousel with featured phones
   - About section
   - Phone grid
   - Contact information

2. **Phone Detail Page** (`/phone/[id]`)
   - Large image/video display
   - Phone specifications
   - Buy button with WhatsApp integration
   - Back navigation

## 🛠️ Setup Instructions

### 1. Clone and Install

```bash
git clone <repository-url>
cd highendphones
npm install
```

### 2. Environment Configuration

Copy `.env.local.example` to `.env.local` and update with your credentials:

```bash
cp .env.local.example .env.local
```

Update the following variables in `.env.local`:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your_project-default-rtdb.firebaseio.com/
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# WhatsApp Configuration
NEXT_PUBLIC_WHATSAPP_NUMBER=************
```

### 3. Firebase Setup

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Realtime Database
3. Set up database rules (for development):
```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```
4. Add your Firebase config to `.env.local`

### 4. Cloudinary Setup (Optional)

1. Create account at [Cloudinary](https://cloudinary.com/)
2. Get your cloud name, API key, and API secret
3. Add credentials to `.env.local`

### 5. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the website.

## 📊 Database Structure

The Firebase Realtime Database should have the following structure:

```json
{
  "phones": {
    "phone_id_1": {
      "name": "iPhone 15 Pro Max",
      "storage": "256GB",
      "price": 180000,
      "description": "Latest iPhone with titanium design...",
      "imageUrl": "https://example.com/image.jpg",
      "videoUrl": "https://example.com/video.mp4",
      "featured": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## 🎨 Customization

### Colors
The website uses a golden yellow and black theme. To customize colors, update:
- `src/app/globals.css` - CSS custom properties
- Tailwind classes throughout components

### Logo
Replace `public/logo.svg` with your custom logo.

### Content
- Update company information in `src/app/page.tsx`
- Modify contact details and business hours
- Update WhatsApp number in environment variables

## 📱 WhatsApp Integration

The "Buy Now" buttons automatically generate WhatsApp messages with:
- Phone name and storage
- Price in KSh
- Professional inquiry message

Messages are sent to the number specified in `NEXT_PUBLIC_WHATSAPP_NUMBER`.

## 🚀 Deployment

### Vercel (Recommended)

1. Push code to GitHub
2. Connect repository to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📝 Demo Data

The website includes demo data that displays when Firebase is not configured. This includes:
- iPhone 15 Pro Max
- Samsung Galaxy S24 Ultra
- Google Pixel 8 Pro
- OnePlus 12

## 🔧 Development

### Adding New Phones

1. Add phone data to Firebase Realtime Database
2. Include required fields: name, storage, price, description
3. Optional: imageUrl, videoUrl, featured flag

### Modifying Components

- `src/components/Navigation.tsx` - Header and search
- `src/components/HeroCarousel.tsx` - Featured phones carousel
- `src/components/PhoneCard.tsx` - Phone grid items
- `src/app/page.tsx` - Main page layout
- `src/app/phone/[id]/page.tsx` - Phone detail pages

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Support

For support or questions:
- Email: <EMAIL>
- WhatsApp: +254 700 000 000

---

Built with ❤️ for High End Phones.ke
