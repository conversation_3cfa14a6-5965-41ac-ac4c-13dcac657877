'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Phone } from '@/types/phone';
import { FiChevronLeft, FiChevronRight, FiShoppingCart } from 'react-icons/fi';

interface HeroCarouselProps {
  featuredPhones: Phone[];
}

export default function HeroCarousel({ featuredPhones }: HeroCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides
  useEffect(() => {
    if (featuredPhones.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);
      }, 5000);
      return () => clearInterval(timer);
    }
  }, [featuredPhones.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredPhones.length) % featuredPhones.length);
  };

  const handleBuyClick = (phone: Phone) => {
    const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;
    const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!featuredPhones.length) {
    return (
      <div className="relative h-96 bg-gradient-black-gold flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-4xl font-bold mb-4">Welcome to High End Phones.ke</h2>
          <p className="text-xl">Your premium phone destination</p>
        </div>
      </div>
    );
  }

  const currentPhone = featuredPhones[currentSlide];

  return (
    <div className="relative h-[600px] md:h-[700px] overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 gradient-black-gold">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-yellow-300 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-yellow-500 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>
      </div>

      {/* Phone Image Background */}
      {currentPhone.imageUrl && (
        <div className="absolute inset-0 opacity-20">
          <Image
            src={currentPhone.imageUrl}
            alt={currentPhone.name}
            fill
            className="object-cover"
          />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="text-white space-y-6">
              <div className="space-y-2">
                <span className="inline-block px-4 py-2 bg-yellow-500/20 text-yellow-300 rounded-full text-sm font-medium backdrop-blur-sm">
                  ✨ Premium Collection
                </span>
                <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                  {currentPhone.name}
                </h1>
              </div>

              <div className="flex items-center space-x-4">
                <span className="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-yellow-300 font-medium">
                  {currentPhone.storage}
                </span>
                <span className="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-green-300 font-medium">
                  ✓ In Stock
                </span>
              </div>

              <div className="space-y-2">
                <p className="text-6xl md:text-7xl font-bold text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">
                  KSh {currentPhone.price.toLocaleString()}
                </p>
                <p className="text-gray-300">Free delivery • 1 year warranty</p>
              </div>

              {currentPhone.description && (
                <p className="text-xl text-gray-200 max-w-lg leading-relaxed">
                  {currentPhone.description}
                </p>
              )}

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <button
                  onClick={() => handleBuyClick(currentPhone)}
                  className="group gradient-gold text-black font-bold py-4 px-8 rounded-full hover-glow transition-all duration-300 flex items-center justify-center space-x-3 text-lg"
                >
                  <FiShoppingCart size={24} className="group-hover:scale-110 transition-transform" />
                  <span>Buy Now</span>
                </button>
                <Link
                  href={`/phone/${currentPhone.id}`}
                  className="glass text-white font-semibold py-4 px-8 rounded-full hover:bg-white/20 transition-all duration-300 text-center text-lg backdrop-blur-sm"
                >
                  View Details
                </Link>
              </div>

              {/* Features */}
              <div className="flex items-center space-x-6 pt-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-gray-300">Authentic</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="text-gray-300">Fast Delivery</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span className="text-gray-300">Best Price</span>
                </div>
              </div>
            </div>

            {/* Phone Image (visible on larger screens) */}
            <div className="hidden lg:block relative">
              {currentPhone.imageUrl && (
                <div className="relative h-96 w-full">
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-transparent rounded-3xl blur-3xl"></div>
                  <Image
                    src={currentPhone.imageUrl}
                    alt={currentPhone.name}
                    fill
                    className="object-contain hover-scale relative z-10"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      {featuredPhones.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200"
          >
            <FiChevronLeft size={24} />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200"
          >
            <FiChevronRight size={24} />
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {featuredPhones.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {featuredPhones.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentSlide ? 'bg-yellow-400' : 'bg-white bg-opacity-50'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
