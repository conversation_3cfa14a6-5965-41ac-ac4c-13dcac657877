'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Phone } from '@/types/phone';
import { FiChevronLeft, FiChevronRight, FiShoppingCart } from 'react-icons/fi';

interface HeroCarouselProps {
  featuredPhones: Phone[];
}

export default function HeroCarousel({ featuredPhones }: HeroCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides
  useEffect(() => {
    if (featuredPhones.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);
      }, 5000);
      return () => clearInterval(timer);
    }
  }, [featuredPhones.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredPhones.length) % featuredPhones.length);
  };

  const handleBuyClick = (phone: Phone) => {
    const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;
    const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!featuredPhones.length) {
    return (
      <div className="relative h-96 bg-gradient-black-gold flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-4xl font-bold mb-4">Welcome to High End Phones.ke</h2>
          <p className="text-xl">Your premium phone destination</p>
        </div>
      </div>
    );
  }

  const currentPhone = featuredPhones[currentSlide];

  return (
    <div className="relative h-96 md:h-[500px] overflow-hidden">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 gradient-black-gold"></div>
      
      {/* Phone Image */}
      {currentPhone.imageUrl && (
        <div className="absolute inset-0 opacity-30">
          <Image
            src={currentPhone.imageUrl}
            alt={currentPhone.name}
            fill
            className="object-cover"
          />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            {/* Text Content */}
            <div className="text-white">
              <h1 className="text-4xl md:text-6xl font-bold mb-4">
                {currentPhone.name}
              </h1>
              <p className="text-xl md:text-2xl mb-2 text-yellow-300">
                {currentPhone.storage}
              </p>
              <p className="text-3xl md:text-4xl font-bold mb-6 text-yellow-400">
                KSh {currentPhone.price.toLocaleString()}
              </p>
              {currentPhone.description && (
                <p className="text-lg mb-8 text-gray-200 max-w-md">
                  {currentPhone.description}
                </p>
              )}
              
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={() => handleBuyClick(currentPhone)}
                  className="gradient-gold text-black font-semibold py-3 px-8 rounded-lg hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <FiShoppingCart size={20} />
                  <span>Buy Now</span>
                </button>
                <Link
                  href={`/phone/${currentPhone.id}`}
                  className="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-black transition-all duration-300 text-center"
                >
                  View Details
                </Link>
              </div>
            </div>

            {/* Phone Image (visible on larger screens) */}
            <div className="hidden md:block">
              {currentPhone.imageUrl && (
                <div className="relative h-80 w-full">
                  <Image
                    src={currentPhone.imageUrl}
                    alt={currentPhone.name}
                    fill
                    className="object-contain"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      {featuredPhones.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200"
          >
            <FiChevronLeft size={24} />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200"
          >
            <FiChevronRight size={24} />
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {featuredPhones.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {featuredPhones.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentSlide ? 'bg-yellow-400' : 'bg-white bg-opacity-50'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
