'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ref, get } from 'firebase/database';
import { database } from '@/lib/firebase';
import { Phone } from '@/types/phone';
import Navigation from '@/components/Navigation';
import { FiArrowLeft, FiShoppingCart, FiPlay, FiPause } from 'react-icons/fi';

export default function PhoneDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [phone, setPhone] = useState<Phone | null>(null);
  const [loading, setLoading] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  useEffect(() => {
    const fetchPhone = async () => {
      if (!params.id) return;

      try {
        const phoneRef = ref(database, `phones/${params.id}`);
        const snapshot = await get(phoneRef);
        
        if (snapshot.exists()) {
          setPhone({
            id: params.id as string,
            ...snapshot.val()
          });
        } else {
          // Demo data fallback
          const demoPhones: Record<string, Phone> = {
            '1': {
              id: '1',
              name: 'iPhone 15 Pro Max',
              storage: '256GB',
              price: 180000,
              description: 'The iPhone 15 Pro Max features a titanium design, advanced camera system with 5x telephoto zoom, and the powerful A17 Pro chip. Experience the ultimate in mobile technology with this premium flagship device.',
              imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800',
              videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
              featured: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            '2': {
              id: '2',
              name: 'Samsung Galaxy S24 Ultra',
              storage: '512GB',
              price: 165000,
              description: 'The Galaxy S24 Ultra combines cutting-edge AI features with the iconic S Pen. Featuring a 200MP camera with incredible zoom capabilities and a stunning 6.8-inch Dynamic AMOLED display.',
              imageUrl: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=800',
              featured: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            '3': {
              id: '3',
              name: 'Google Pixel 8 Pro',
              storage: '128GB',
              price: 120000,
              description: 'Experience the pure Android experience with Google Pixel 8 Pro. Featuring advanced AI photography, Magic Eraser, and the latest Google Tensor G3 chip for unmatched performance.',
              imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            '4': {
              id: '4',
              name: 'OnePlus 12',
              storage: '256GB',
              price: 95000,
              description: 'The OnePlus 12 delivers flagship performance with 100W SuperVOOC charging, Snapdragon 8 Gen 3, and a stunning Hasselblad camera system. Never settle for less.',
              imageUrl: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=800',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
          };

          const demoPhone = demoPhones[params.id as string];
          if (demoPhone) {
            setPhone(demoPhone);
          } else {
            router.push('/');
          }
        }
      } catch (error) {
        console.error('Error fetching phone:', error);
        router.push('/');
      } finally {
        setLoading(false);
      }
    };

    fetchPhone();
  }, [params.id, router]);

  const handleBuyClick = () => {
    if (!phone) return;
    
    const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}. Can you provide more details?`;
    const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
            <p className="mt-4 text-gray-600">Loading phone details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!phone) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Phone not found</h1>
            <Link 
              href="/"
              className="gradient-gold text-black font-semibold py-2 px-6 rounded-lg hover:shadow-gold transition-all duration-300"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link 
          href="/"
          className="inline-flex items-center space-x-2 text-gray-600 hover:text-yellow-600 transition-colors duration-200 mb-8"
        >
          <FiArrowLeft size={20} />
          <span>Back to Phones</span>
        </Link>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Image/Video Section */}
          <div className="space-y-4">
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden shadow-lg">
              {phone.videoUrl && isVideoPlaying ? (
                <div className="relative w-full h-full">
                  <video
                    src={phone.videoUrl}
                    controls
                    autoPlay
                    className="w-full h-full object-cover"
                    onEnded={() => setIsVideoPlaying(false)}
                  />
                  <button
                    onClick={() => setIsVideoPlaying(false)}
                    className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200"
                  >
                    <FiPause size={20} />
                  </button>
                </div>
              ) : (
                <div className="relative w-full h-full">
                  {phone.imageUrl ? (
                    <Image
                      src={phone.imageUrl}
                      alt={phone.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gray-100">
                      <div className="text-gray-400 text-center">
                        <FiShoppingCart size={64} />
                        <p className="mt-4">No Image Available</p>
                      </div>
                    </div>
                  )}
                  
                  {phone.videoUrl && (
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-50 transition-all duration-200"
                    >
                      <div className="bg-white bg-opacity-90 rounded-full p-4 hover:bg-opacity-100 transition-all duration-200">
                        <FiPlay size={32} className="text-black ml-1" />
                      </div>
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Details Section */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                {phone.name}
              </h1>
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-lg text-gray-600 bg-gray-100 px-3 py-1 rounded-lg">
                  {phone.storage}
                </span>
                {phone.featured && (
                  <span className="bg-yellow-500 text-black px-3 py-1 rounded-lg text-sm font-semibold">
                    Featured
                  </span>
                )}
              </div>
              <div className="text-4xl font-bold text-yellow-600 mb-6">
                KSh {phone.price.toLocaleString()}
              </div>
            </div>

            {phone.description && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-3">Description</h2>
                <p className="text-gray-700 leading-relaxed">
                  {phone.description}
                </p>
              </div>
            )}

            <div className="space-y-4">
              <button
                onClick={handleBuyClick}
                className="w-full gradient-gold text-black font-semibold py-4 px-8 rounded-lg hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-3 text-lg"
              >
                <FiShoppingCart size={24} />
                <span>Buy Now via WhatsApp</span>
              </button>
              
              <div className="text-center text-sm text-gray-600">
                <p>Secure payment • Fast delivery • 24/7 support</p>
              </div>
            </div>

            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Why Choose High End Phones.ke?</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>Authentic products with warranty</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>Fast delivery across Kenya</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>Competitive pricing</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>Expert customer support</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
