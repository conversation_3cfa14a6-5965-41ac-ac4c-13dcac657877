{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useState } from 'react';\nimport { FiMenu, FiX, FiSearch } from 'react-icons/fi';\n\ninterface NavigationProps {\n  onSearch?: (query: string) => void;\n}\n\nexport default function Navigation({ onSearch }: NavigationProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (onSearch) {\n      onSearch(searchQuery);\n    }\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Image\n              src=\"/logo.svg\"\n              alt=\"High End Phones.ke\"\n              width={40}\n              height={40}\n              className=\"w-10 h-10\"\n            />\n            <span className=\"text-xl font-bold text-gradient\">\n              High End Phones.ke\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link \n              href=\"/\" \n              className=\"text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n            >\n              Home\n            </Link>\n            <Link \n              href=\"#about\" \n              className=\"text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n            >\n              About\n            </Link>\n            <Link \n              href=\"#phones\" \n              className=\"text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n            >\n              Phones\n            </Link>\n            <Link \n              href=\"#contact\" \n              className=\"text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n            >\n              Contact\n            </Link>\n          </div>\n\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"hidden md:flex items-center\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search phones...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\n              />\n              <FiSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            </div>\n          </form>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-md text-gray-700 hover:text-yellow-600 hover:bg-gray-100\"\n          >\n            {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link \n                href=\"/\" \n                className=\"block px-3 py-2 text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link \n                href=\"#about\" \n                className=\"block px-3 py-2 text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                About\n              </Link>\n              <Link \n                href=\"#phones\" \n                className=\"block px-3 py-2 text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Phones\n              </Link>\n              <Link \n                href=\"#contact\" \n                className=\"block px-3 py-2 text-gray-700 hover:text-yellow-600 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Contact\n              </Link>\n              \n              {/* Mobile Search */}\n              <form onSubmit={handleSearch} className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search phones...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\n                  />\n                  <FiSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWe,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAMpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,6LAAC,iJAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;gBAKnD,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAKD,6LAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,6LAAC,iJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GApIwB;KAAA", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/HeroCarousel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Phone } from '@/types/phone';\nimport { FiChevronLeft, FiChevronRight, FiShoppingCart } from 'react-icons/fi';\n\ninterface HeroCarouselProps {\n  featuredPhones: Phone[];\n}\n\nexport default function HeroCarousel({ featuredPhones }: HeroCarouselProps) {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Auto-advance slides\n  useEffect(() => {\n    if (featuredPhones.length > 1) {\n      const timer = setInterval(() => {\n        setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);\n      }, 5000);\n      return () => clearInterval(timer);\n    }\n  }, [featuredPhones.length]);\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + featuredPhones.length) % featuredPhones.length);\n  };\n\n  const handleBuyClick = (phone: Phone) => {\n    const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;\n    const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';\n    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;\n    window.open(whatsappUrl, '_blank');\n  };\n\n  if (!featuredPhones.length) {\n    return (\n      <div className=\"relative h-96 bg-gradient-black-gold flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <h2 className=\"text-4xl font-bold mb-4\">Welcome to High End Phones.ke</h2>\n          <p className=\"text-xl\">Your premium phone destination</p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentPhone = featuredPhones[currentSlide];\n\n  return (\n    <div className=\"relative h-96 md:h-[500px] overflow-hidden\">\n      {/* Background with gradient overlay */}\n      <div className=\"absolute inset-0 gradient-black-gold\"></div>\n      \n      {/* Phone Image */}\n      {currentPhone.imageUrl && (\n        <div className=\"absolute inset-0 opacity-30\">\n          <Image\n            src={currentPhone.imageUrl}\n            alt={currentPhone.name}\n            fill\n            className=\"object-cover\"\n          />\n        </div>\n      )}\n\n      {/* Content */}\n      <div className=\"relative z-10 h-full flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n          <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n            {/* Text Content */}\n            <div className=\"text-white\">\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\n                {currentPhone.name}\n              </h1>\n              <p className=\"text-xl md:text-2xl mb-2 text-yellow-300\">\n                {currentPhone.storage}\n              </p>\n              <p className=\"text-3xl md:text-4xl font-bold mb-6 text-yellow-400\">\n                KSh {currentPhone.price.toLocaleString()}\n              </p>\n              {currentPhone.description && (\n                <p className=\"text-lg mb-8 text-gray-200 max-w-md\">\n                  {currentPhone.description}\n                </p>\n              )}\n              \n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <button\n                  onClick={() => handleBuyClick(currentPhone)}\n                  className=\"gradient-gold text-black font-semibold py-3 px-8 rounded-lg hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-2\"\n                >\n                  <FiShoppingCart size={20} />\n                  <span>Buy Now</span>\n                </button>\n                <Link\n                  href={`/phone/${currentPhone.id}`}\n                  className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-black transition-all duration-300 text-center\"\n                >\n                  View Details\n                </Link>\n              </div>\n            </div>\n\n            {/* Phone Image (visible on larger screens) */}\n            <div className=\"hidden md:block\">\n              {currentPhone.imageUrl && (\n                <div className=\"relative h-80 w-full\">\n                  <Image\n                    src={currentPhone.imageUrl}\n                    alt={currentPhone.name}\n                    fill\n                    className=\"object-contain\"\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {featuredPhones.length > 1 && (\n        <>\n          <button\n            onClick={prevSlide}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200\"\n          >\n            <FiChevronLeft size={24} />\n          </button>\n          <button\n            onClick={nextSlide}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200\"\n          >\n            <FiChevronRight size={24} />\n          </button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {featuredPhones.length > 1 && (\n        <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n          {featuredPhones.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentSlide(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentSlide ? 'bg-yellow-400' : 'bg-white bg-opacity-50'\n              }`}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAmC2B;;AAjC3B;AACA;AACA;AAEA;;;AANA;;;;;AAYe,SAAS,aAAa,EAAE,cAAc,EAAqB;;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,QAAQ;oDAAY;wBACxB;4DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;;oBAC9D;mDAAG;gBACH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;IAC9D;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM;IACtF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,CAAC,0BAA0B,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,cAAc,IAAI;QACpH,MAAM,iBAAiB,oDAA2C;QAClE,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU;QACzF,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,IAAI,CAAC,eAAe,MAAM,EAAE;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,MAAM,eAAe,cAAc,CAAC,aAAa;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;YAGd,aAAa,QAAQ,kBACpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,aAAa,QAAQ;oBAC1B,KAAK,aAAa,IAAI;oBACtB,IAAI;oBACJ,WAAU;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,aAAa,IAAI;;;;;;kDAEpB,6LAAC;wCAAE,WAAU;kDACV,aAAa,OAAO;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;;4CAAsD;4CAC5D,aAAa,KAAK,CAAC,cAAc;;;;;;;oCAEvC,aAAa,WAAW,kBACvB,6LAAC;wCAAE,WAAU;kDACV,aAAa,WAAW;;;;;;kDAI7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,6LAAC,iJAAA,CAAA,iBAAc;wDAAC,MAAM;;;;;;kEACtB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE;gDACjC,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAOL,6LAAC;gCAAI,WAAU;0CACZ,aAAa,QAAQ,kBACpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,aAAa,QAAQ;wCAC1B,KAAK,aAAa,IAAI;wCACtB,IAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUvB,eAAe,MAAM,GAAG,mBACvB;;kCACE,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,iJAAA,CAAA,gBAAa;4BAAC,MAAM;;;;;;;;;;;kCAEvB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;4BAAC,MAAM;;;;;;;;;;;;;YAM3B,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eAAe,kBAAkB,0BAC3C;uBAJG;;;;;;;;;;;;;;;;AAWnB;GAnJwB;KAAA", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/PhoneCard.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Phone } from '@/types/phone';\nimport { FiShoppingCart } from 'react-icons/fi';\n\ninterface PhoneCardProps {\n  phone: Phone;\n  onBuy?: (phone: Phone) => void;\n}\n\nexport default function PhoneCard({ phone, onBuy }: PhoneCardProps) {\n  const handleBuyClick = () => {\n    if (onBuy) {\n      onBuy(phone);\n    } else {\n      // Default WhatsApp integration\n      const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;\n      const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';\n      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;\n      window.open(whatsappUrl, '_blank');\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover-lift overflow-hidden\">\n      {/* Phone Image */}\n      <Link href={`/phone/${phone.id}`}>\n        <div className=\"relative h-64 bg-gray-100\">\n          {phone.imageUrl ? (\n            <Image\n              src={phone.imageUrl}\n              alt={phone.name}\n              fill\n              className=\"object-cover hover:scale-105 transition-transform duration-300\"\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-gray-400 text-center\">\n                <FiShoppingCart size={48} />\n                <p className=\"mt-2\">No Image</p>\n              </div>\n            </div>\n          )}\n          {phone.featured && (\n            <div className=\"absolute top-2 left-2 bg-yellow-500 text-black px-2 py-1 rounded-md text-xs font-semibold\">\n              Featured\n            </div>\n          )}\n        </div>\n      </Link>\n\n      {/* Phone Details */}\n      <div className=\"p-4\">\n        <Link href={`/phone/${phone.id}`}>\n          <h3 className=\"text-lg font-semibold text-gray-900 hover:text-yellow-600 transition-colors duration-200 mb-2\">\n            {phone.name}\n          </h3>\n        </Link>\n        \n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n            {phone.storage}\n          </span>\n          <span className=\"text-xl font-bold text-yellow-600\">\n            KSh {phone.price.toLocaleString()}\n          </span>\n        </div>\n\n        {phone.description && (\n          <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n            {phone.description}\n          </p>\n        )}\n\n        {/* Buy Button */}\n        <button\n          onClick={handleBuyClick}\n          className=\"w-full gradient-gold text-black font-semibold py-2 px-4 rounded-lg hover:shadow-gold transition-all duration-300 flex items-center justify-center space-x-2\"\n        >\n          <FiShoppingCart size={18} />\n          <span>Buy Now</span>\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAmB6B;;AAjB7B;AACA;AAEA;AALA;;;;;AAYe,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAkB;IAChE,MAAM,iBAAiB;QACrB,IAAI,OAAO;YACT,MAAM;QACR,OAAO;YACL,+BAA+B;YAC/B,MAAM,UAAU,CAAC,0BAA0B,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,cAAc,IAAI;YACpH,MAAM,iBAAiB,oDAA2C;YAClE,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU;YACzF,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,QAAQ,iBACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,QAAQ;4BACnB,KAAK,MAAM,IAAI;4BACf,IAAI;4BACJ,WAAU;;;;;iDAGZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,iBAAc;wCAAC,MAAM;;;;;;kDACtB,6LAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;;;;;;wBAIzB,MAAM,QAAQ,kBACb,6LAAC;4BAAI,WAAU;sCAA4F;;;;;;;;;;;;;;;;;0BAQjH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;kCAC9B,cAAA,6LAAC;4BAAG,WAAU;sCACX,MAAM,IAAI;;;;;;;;;;;kCAIf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,MAAM,OAAO;;;;;;0CAEhB,6LAAC;gCAAK,WAAU;;oCAAoC;oCAC7C,MAAM,KAAK,CAAC,cAAc;;;;;;;;;;;;;oBAIlC,MAAM,WAAW,kBAChB,6LAAC;wBAAE,WAAU;kCACV,MAAM,WAAW;;;;;;kCAKtB,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,MAAM;;;;;;0CACtB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;KA3EwB", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ref, onValue } from 'firebase/database';\nimport { database } from '@/lib/firebase';\nimport { Phone } from '@/types/phone';\nimport Navigation from '@/components/Navigation';\nimport HeroCarousel from '@/components/HeroCarousel';\nimport PhoneCard from '@/components/PhoneCard';\nimport { FiMapPin, FiPhone, FiMail, FiClock } from 'react-icons/fi';\n\nexport default function Home() {\n  const [phones, setPhones] = useState<Phone[]>([]);\n  const [filteredPhones, setFilteredPhones] = useState<Phone[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  // Fetch phones from Firebase\n  useEffect(() => {\n    const phonesRef = ref(database, 'phones');\n    const unsubscribe = onValue(phonesRef, (snapshot) => {\n      const data = snapshot.val();\n      if (data) {\n        const phonesList = Object.keys(data).map(key => ({\n          id: key,\n          ...data[key]\n        }));\n        setPhones(phonesList);\n        setFilteredPhones(phonesList);\n      } else {\n        // Demo data if no Firebase data\n        const demoPhones: Phone[] = [\n          {\n            id: '1',\n            name: 'iPhone 15 Pro Max',\n            storage: '256GB',\n            price: 180000,\n            description: 'Latest iPhone with titanium design and advanced camera system',\n            imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',\n            featured: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '2',\n            name: 'Samsung Galaxy S24 Ultra',\n            storage: '512GB',\n            price: 165000,\n            description: 'Premium Android flagship with S Pen and incredible camera zoom',\n            imageUrl: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',\n            featured: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '3',\n            name: 'Google Pixel 8 Pro',\n            storage: '128GB',\n            price: 120000,\n            description: 'Pure Android experience with exceptional AI photography',\n            imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '4',\n            name: 'OnePlus 12',\n            storage: '256GB',\n            price: 95000,\n            description: 'Fast charging flagship with premium performance',\n            imageUrl: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=500',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          }\n        ];\n        setPhones(demoPhones);\n        setFilteredPhones(demoPhones);\n      }\n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  // Filter phones based on search query\n  useEffect(() => {\n    if (searchQuery.trim() === '') {\n      setFilteredPhones(phones);\n    } else {\n      const filtered = phones.filter(phone =>\n        phone.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        phone.storage.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        phone.description?.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredPhones(filtered);\n    }\n  }, [searchQuery, phones]);\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n  };\n\n  const featuredPhones = phones.filter(phone => phone.featured);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation onSearch={handleSearch} />\n\n      {/* Hero Section */}\n      <HeroCarousel featuredPhones={featuredPhones} />\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              About High End Phones.ke\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n              We are Kenya's premier destination for high-end smartphones. Our commitment to quality,\n              competitive pricing, and exceptional customer service makes us the trusted choice for\n              premium mobile devices.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiPhone className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Premium Quality</h3>\n              <p className=\"text-gray-600\">Only the finest smartphones from trusted brands</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiMapPin className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Kenya-wide Delivery</h3>\n              <p className=\"text-gray-600\">Fast and secure delivery across Kenya</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiClock className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">24/7 Support</h3>\n              <p className=\"text-gray-600\">Round-the-clock customer support</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Phones Section */}\n      <section id=\"phones\" className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Our Premium Collection\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Discover the latest high-end smartphones with cutting-edge technology\n            </p>\n          </div>\n\n          {loading ? (\n            <div className=\"text-center py-12\">\n              <div className=\"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600\"></div>\n              <p className=\"mt-4 text-gray-600\">Loading phones...</p>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {filteredPhones.map((phone) => (\n                <PhoneCard key={phone.id} phone={phone} />\n              ))}\n            </div>\n          )}\n\n          {!loading && filteredPhones.length === 0 && (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-600 text-lg\">No phones found matching your search.</p>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Get in Touch\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Ready to upgrade your phone? Contact us today!\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Contact Information</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <FiPhone className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\">+254 700 000 000</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <FiMail className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <FiMapPin className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\">Nairobi, Kenya</span>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Business Hours</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Monday - Friday</span>\n                  <span className=\"text-gray-900 font-medium\">9:00 AM - 6:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Saturday</span>\n                  <span className=\"text-gray-900 font-medium\">10:00 AM - 4:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Sunday</span>\n                  <span className=\"text-gray-900 font-medium\">Closed</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-black text-white py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-4\">High End Phones.ke</h3>\n            <p className=\"text-gray-400 mb-4\">\n              Your trusted partner for premium smartphones in Kenya\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 High End Phones.ke. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;;;;AAGA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,YAAY,CAAA,GAAA,qKAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAChC,MAAM,cAAc,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE;8CAAW,CAAC;oBACtC,MAAM,OAAO,SAAS,GAAG;oBACzB,IAAI,MAAM;wBACR,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,GAAG;qEAAC,CAAA,MAAO,CAAC;oCAC/C,IAAI;oCACJ,GAAG,IAAI,CAAC,IAAI;gCACd,CAAC;;wBACD,UAAU;wBACV,kBAAkB;oBACpB,OAAO;wBACL,gCAAgC;wBAChC,MAAM,aAAsB;4BAC1B;gCACE,IAAI;gCACJ,MAAM;gCACN,SAAS;gCACT,OAAO;gCACP,aAAa;gCACb,UAAU;gCACV,UAAU;gCACV,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,SAAS;gCACT,OAAO;gCACP,aAAa;gCACb,UAAU;gCACV,UAAU;gCACV,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,SAAS;gCACT,OAAO;gCACP,aAAa;gCACb,UAAU;gCACV,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,SAAS;gCACT,OAAO;gCACP,aAAa;gCACb,UAAU;gCACV,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;wBACD,UAAU;wBACV,kBAAkB;oBACpB;oBACA,WAAW;gBACb;;YAEA;kCAAO,IAAM;;QACf;yBAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,YAAY,IAAI,OAAO,IAAI;gBAC7B,kBAAkB;YACpB,OAAO;gBACL,MAAM,WAAW,OAAO,MAAM;+CAAC,CAAA,QAC7B,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,MAAM,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW;;gBAEnE,kBAAkB;YACpB;QACF;yBAAG;QAAC;QAAa;KAAO;IAExB,MAAM,eAAe,CAAC;QACpB,eAAe;IACjB;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;IAE5D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;gBAAC,UAAU;;;;;;0BAGtB,6LAAC,qIAAA,CAAA,UAAY;gBAAC,gBAAgB;;;;;;0BAG9B,6LAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAKtC,wBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;iDAGpC,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC,kIAAA,CAAA,UAAS;oCAAgB,OAAO;mCAAjB,MAAM,EAAE;;;;;;;;;;wBAK7B,CAAC,WAAW,eAAe,MAAM,KAAK,mBACrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAKtC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAnPwB;KAAA", "debugId": null}}]}