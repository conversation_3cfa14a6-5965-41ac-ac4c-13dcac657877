{"version": 3, "sources": [], "sections": [{"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getDatabase } from 'firebase/database';\n\nconst firebaseConfig = {\n  // Add your Firebase config here\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n};\n\nconst app = initializeApp(firebaseConfig);\nexport const database = getDatabase(app);\n\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEA,MAAM,iBAAiB;IACrB,gCAAgC;IAChC,MAAM;IACN,UAAU;IACV,WAAW;IACX,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;AACnB,MAAM,WAAW,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useState } from 'react';\nimport { FiMenu, FiX, FiSearch, FiShoppingBag, FiHeart, FiUser } from 'react-icons/fi';\n\ninterface NavigationProps {\n  onSearch?: (query: string) => void;\n  onCategoryFilter?: (category: string) => void;\n}\n\nexport default function Navigation({ onSearch, onCategoryFilter }: NavigationProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (onSearch) {\n      onSearch(searchQuery);\n    }\n  };\n\n  const categories = [\n    { name: 'iPhone', icon: '📱' },\n    { name: 'Samsung', icon: '📲' },\n    { name: 'Google', icon: '🔍' },\n    { name: 'OnePlus', icon: '⚡' },\n    { name: 'Deals', icon: '🔥' }\n  ];\n\n  return (\n    <>\n      {/* Top Bar */}\n      <div className=\"bg-gradient-black-gold text-white py-2 text-center text-sm\">\n        <p>🚚 Free delivery in Nairobi • 📞 Call us: +254 700 000 000</p>\n      </div>\n\n      {/* Main Navigation */}\n      <nav className=\"glass sticky top-0 z-50 border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex items-center space-x-3 hover-scale\">\n              <div className=\"relative\">\n                <Image\n                  src=\"/logo.svg\"\n                  alt=\"High End Phones.ke\"\n                  width={48}\n                  height={48}\n                  className=\"w-12 h-12\"\n                />\n                <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-gradient-gold rounded-full pulse-gold\"></div>\n              </div>\n              <div>\n                <span className=\"text-2xl font-bold text-gradient block\">\n                  High End Phones\n                </span>\n                <span className=\"text-xs text-gray-600 font-medium\">.ke</span>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group\"\n              >\n                Home\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n\n              {/* Categories Dropdown */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium flex items-center space-x-1\">\n                  <span>Categories</span>\n                  <svg className=\"w-4 h-4 transition-transform group-hover:rotate-180\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* Dropdown Menu */}\n                <div className=\"absolute top-full left-0 mt-2 w-48 glass rounded-xl shadow-soft opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\">\n                  <div className=\"p-2\">\n                    {categories.map((category) => (\n                      <button\n                        key={category.name}\n                        onClick={() => onCategoryFilter?.(category.name)}\n                        className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 rounded-lg transition-all duration-200\"\n                      >\n                        <span className=\"text-lg\">{category.icon}</span>\n                        <span className=\"font-medium\">{category.name}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              <a\n                href=\"#about\"\n                className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group\"\n              >\n                About\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full\"></span>\n              </a>\n              <a\n                href=\"#contact\"\n                className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium relative group\"\n              >\n                Contact\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-gold transition-all duration-300 group-hover:w-full\"></span>\n              </a>\n            </div>\n\n            {/* Search Bar */}\n            <form onSubmit={handleSearch} className=\"hidden md:flex items-center\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search phones, brands...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-80 pl-12 pr-4 py-3 glass rounded-full focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 placeholder-gray-500\"\n                />\n                <FiSearch className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg\" />\n                <button\n                  type=\"submit\"\n                  className=\"absolute right-2 top-1/2 transform -translate-y-1/2 gradient-gold text-black p-2 rounded-full hover-glow transition-all duration-300\"\n                >\n                  <FiSearch size={16} />\n                </button>\n              </div>\n            </form>\n\n            {/* Action Icons */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              <button className=\"p-3 glass rounded-full hover-glow transition-all duration-300 relative\">\n                <FiHeart size={20} />\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">2</span>\n              </button>\n              <button className=\"p-3 glass rounded-full hover-glow transition-all duration-300 relative\">\n                <FiShoppingBag size={20} />\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-gradient-gold text-black text-xs rounded-full flex items-center justify-center\">0</span>\n              </button>\n              <button className=\"p-3 glass rounded-full hover-glow transition-all duration-300\">\n                <FiUser size={20} />\n              </button>\n            </div>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-3 glass rounded-full hover-glow transition-all duration-300\"\n            >\n              {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n            </button>\n          </div>\n\n          {/* Mobile Navigation */}\n          {isMenuOpen && (\n            <div className=\"lg:hidden\">\n              <div className=\"px-2 pt-2 pb-6 space-y-1 glass-dark rounded-b-xl mt-2\">\n                {/* Mobile Search */}\n                <form onSubmit={handleSearch} className=\"px-3 py-4\">\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search phones...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      className=\"w-full pl-12 pr-4 py-3 glass rounded-full focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\n                    />\n                    <FiSearch className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                  </div>\n                </form>\n\n                {/* Mobile Menu Items */}\n                <Link\n                  href=\"/\"\n                  className=\"block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  🏠 Home\n                </Link>\n\n                {/* Mobile Categories */}\n                <div className=\"px-6 py-2\">\n                  <p className=\"text-gray-400 text-sm font-medium mb-2\">Categories</p>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {categories.map((category) => (\n                      <button\n                        key={category.name}\n                        onClick={() => {\n                          onCategoryFilter?.(category.name);\n                          setIsMenuOpen(false);\n                        }}\n                        className=\"flex items-center space-x-2 px-4 py-2 text-white hover:bg-white/10 rounded-lg transition-all duration-200\"\n                      >\n                        <span>{category.icon}</span>\n                        <span className=\"text-sm\">{category.name}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <a\n                  href=\"#about\"\n                  className=\"block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  ℹ️ About\n                </a>\n                <a\n                  href=\"#contact\"\n                  className=\"block px-6 py-3 text-white hover:bg-white/10 rounded-lg transition-all duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  📞 Contact\n                </a>\n              </div>\n            </div>\n          )}\n        </div>\n      </nav>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYe,SAAS,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAmB;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAU,MAAM;QAAK;QAC7B;YAAE,MAAM;YAAW,MAAM;QAAK;QAC9B;YAAE,MAAM;YAAU,MAAM;QAAK;QAC7B;YAAE,MAAM;YAAW,MAAM;QAAI;QAC7B;YAAE,MAAM;YAAS,MAAM;QAAK;KAC7B;IAED,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAyC;;;;;;8DAGzD,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;8CAKxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAI,WAAU;4DAAsD,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC7G,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAKzE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gEAEC,SAAS,IAAM,mBAAmB,SAAS,IAAI;gEAC/C,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAW,SAAS,IAAI;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAe,SAAS,IAAI;;;;;;;+DALvC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;sDAY5B,8OAAC;4CACC,MAAK;4CACL,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAElB,8OAAC;4CACC,MAAK;4CACL,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;8CAKpB,8OAAC;oCAAK,UAAU;oCAAc,WAAU;8CACtC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,8OAAC,8IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDACC,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,8IAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;8CAMtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,8IAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAA+G;;;;;;;;;;;;sDAEjI,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,8IAAA,CAAA,gBAAa;oDAAC,MAAM;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAqH;;;;;;;;;;;;sDAEvI,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC,8IAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BAAa,8OAAC,8IAAA,CAAA,MAAG;wCAAC,MAAM;;;;;6DAAS,8OAAC,8IAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;wBAKnD,4BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAK,UAAU;wCAAc,WAAU;kDACtC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;8DAEZ,8OAAC,8IAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKxB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAKD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAyC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wDAEC,SAAS;4DACP,mBAAmB,SAAS,IAAI;4DAChC,cAAc;wDAChB;wDACA,WAAU;;0EAEV,8OAAC;0EAAM,SAAS,IAAI;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAAW,SAAS,IAAI;;;;;;;uDARnC,SAAS,IAAI;;;;;;;;;;;;;;;;kDAc1B,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/HeroCarousel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Phone } from '@/types/phone';\nimport { FiChevronLeft, FiChevronRight, FiShoppingCart } from 'react-icons/fi';\n\ninterface HeroCarouselProps {\n  featuredPhones: Phone[];\n}\n\nexport default function HeroCarousel({ featuredPhones }: HeroCarouselProps) {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Auto-advance slides\n  useEffect(() => {\n    if (featuredPhones.length > 1) {\n      const timer = setInterval(() => {\n        setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);\n      }, 5000);\n      return () => clearInterval(timer);\n    }\n  }, [featuredPhones.length]);\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % featuredPhones.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + featuredPhones.length) % featuredPhones.length);\n  };\n\n  const handleBuyClick = (phone: Phone) => {\n    const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;\n    const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';\n    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;\n    window.open(whatsappUrl, '_blank');\n  };\n\n  if (!featuredPhones.length) {\n    return (\n      <div className=\"relative h-96 bg-gradient-black-gold flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <h2 className=\"text-4xl font-bold mb-4\">Welcome to High End Phones.ke</h2>\n          <p className=\"text-xl\">Your premium phone destination</p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentPhone = featuredPhones[currentSlide];\n\n  return (\n    <div className=\"relative h-[600px] md:h-[700px] overflow-hidden\">\n      {/* Animated Background */}\n      <div className=\"absolute inset-0 gradient-black-gold\">\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"absolute top-10 left-10 w-32 h-32 bg-yellow-400 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-20 right-20 w-40 h-40 bg-yellow-300 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/3 w-24 h-24 bg-yellow-500 rounded-full blur-2xl animate-pulse delay-500\"></div>\n        </div>\n      </div>\n\n      {/* Phone Image Background */}\n      {currentPhone.imageUrl && (\n        <div className=\"absolute inset-0 opacity-20\">\n          <Image\n            src={currentPhone.imageUrl}\n            alt={currentPhone.name}\n            fill\n            className=\"object-cover\"\n          />\n        </div>\n      )}\n\n      {/* Content */}\n      <div className=\"relative z-10 h-full flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Text Content */}\n            <div className=\"text-white space-y-6\">\n              <div className=\"space-y-2\">\n                <span className=\"inline-block px-4 py-2 bg-yellow-500/20 text-yellow-300 rounded-full text-sm font-medium backdrop-blur-sm\">\n                  ✨ Premium Collection\n                </span>\n                <h1 className=\"text-5xl md:text-7xl font-bold leading-tight\">\n                  {currentPhone.name}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-yellow-300 font-medium\">\n                  {currentPhone.storage}\n                </span>\n                <span className=\"px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-green-300 font-medium\">\n                  ✓ In Stock\n                </span>\n              </div>\n\n              <div className=\"space-y-2\">\n                <p className=\"text-6xl md:text-7xl font-bold text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent\">\n                  KSh {currentPhone.price.toLocaleString()}\n                </p>\n                <p className=\"text-gray-300\">Free delivery • 1 year warranty</p>\n              </div>\n\n              {currentPhone.description && (\n                <p className=\"text-xl text-gray-200 max-w-lg leading-relaxed\">\n                  {currentPhone.description}\n                </p>\n              )}\n\n              <div className=\"flex flex-col sm:flex-row gap-4 pt-4\">\n                <button\n                  onClick={() => handleBuyClick(currentPhone)}\n                  className=\"group gradient-gold text-black font-bold py-4 px-8 rounded-full hover-glow transition-all duration-300 flex items-center justify-center space-x-3 text-lg\"\n                >\n                  <FiShoppingCart size={24} className=\"group-hover:scale-110 transition-transform\" />\n                  <span>Buy Now</span>\n                </button>\n                <Link\n                  href={`/phone/${currentPhone.id}`}\n                  className=\"glass text-white font-semibold py-4 px-8 rounded-full hover:bg-white/20 transition-all duration-300 text-center text-lg backdrop-blur-sm\"\n                >\n                  View Details\n                </Link>\n              </div>\n\n              {/* Features */}\n              <div className=\"flex items-center space-x-6 pt-6 text-sm\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Authentic</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Fast Delivery</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Best Price</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Phone Image (visible on larger screens) */}\n            <div className=\"hidden lg:block relative\">\n              {currentPhone.imageUrl && (\n                <div className=\"relative h-96 w-full\">\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-transparent rounded-3xl blur-3xl\"></div>\n                  <Image\n                    src={currentPhone.imageUrl}\n                    alt={currentPhone.name}\n                    fill\n                    className=\"object-contain hover-scale relative z-10\"\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {featuredPhones.length > 1 && (\n        <>\n          <button\n            onClick={prevSlide}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200\"\n          >\n            <FiChevronLeft size={24} />\n          </button>\n          <button\n            onClick={nextSlide}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200\"\n          >\n            <FiChevronRight size={24} />\n          </button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {featuredPhones.length > 1 && (\n        <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n          {featuredPhones.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentSlide(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentSlide ? 'bg-yellow-400' : 'bg-white bg-opacity-50'\n              }`}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAYe,SAAS,aAAa,EAAE,cAAc,EAAqB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,QAAQ,YAAY;gBACxB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;YAC9D,GAAG;YACH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;IAC9D;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM;IACtF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,CAAC,0BAA0B,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,cAAc,IAAI;QACpH,MAAM,iBAAiB,oDAA2C;QAClE,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU;QACzF,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,IAAI,CAAC,eAAe,MAAM,EAAE;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,MAAM,eAAe,cAAc,CAAC,aAAa;IAEjD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;YAKlB,aAAa,QAAQ,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,aAAa,QAAQ;oBAC1B,KAAK,aAAa,IAAI;oBACtB,IAAI;oBACJ,WAAU;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4G;;;;;;0DAG5H,8OAAC;gDAAG,WAAU;0DACX,aAAa,IAAI;;;;;;;;;;;;kDAItB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,aAAa,OAAO;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;0DAAiF;;;;;;;;;;;;kDAKnG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;oDAA4H;oDAClI,aAAa,KAAK,CAAC,cAAc;;;;;;;0DAExC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;oCAG9B,aAAa,WAAW,kBACvB,8OAAC;wCAAE,WAAU;kDACV,aAAa,WAAW;;;;;;kDAI7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,8OAAC,8IAAA,CAAA,iBAAc;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEACpC,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE;gDACjC,WAAU;0DACX;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,QAAQ,kBACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,aAAa,QAAQ;4CAC1B,KAAK,aAAa,IAAI;4CACtB,IAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUvB,eAAe,MAAM,GAAG,mBACvB;;kCACE,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,8IAAA,CAAA,gBAAa;4BAAC,MAAM;;;;;;;;;;;kCAEvB,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;4BAAC,MAAM;;;;;;;;;;;;;YAM3B,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eAAe,kBAAkB,0BAC3C;uBAJG;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/components/PhoneCard.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Phone } from '@/types/phone';\nimport { FiShoppingCart, FiHeart, FiEye, FiStar, FiZap } from 'react-icons/fi';\n\ninterface PhoneCardProps {\n  phone: Phone;\n  onBuy?: (phone: Phone) => void;\n  isLoading?: boolean;\n}\n\nexport default function PhoneCard({ phone, onBuy, isLoading }: PhoneCardProps) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  const handleBuyClick = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (onBuy) {\n      onBuy(phone);\n    } else {\n      // Default WhatsApp integration\n      const message = `Hi! I'm interested in the ${phone.name} (${phone.storage}) for KSh ${phone.price.toLocaleString()}`;\n      const whatsappNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '254700000000';\n      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;\n      window.open(whatsappUrl, '_blank');\n    }\n  };\n\n  const handleLikeClick = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsLiked(!isLiked);\n  };\n\n  // Determine badge type\n  const getBadge = () => {\n    if (phone.featured) return { text: 'Featured', class: 'badge-hot' };\n    if (phone.name.toLowerCase().includes('new')) return { text: 'New', class: 'badge-new' };\n    if (phone.price < 100000) return { text: 'Deal', class: 'badge-sale' };\n    return null;\n  };\n\n  const badge = getBadge();\n\n  if (isLoading) {\n    return (\n      <div className=\"neu-card overflow-hidden\">\n        {/* Skeleton Image */}\n        <div className=\"h-64 skeleton\"></div>\n\n        {/* Skeleton Content */}\n        <div className=\"p-6 space-y-4\">\n          <div className=\"h-6 skeleton rounded\"></div>\n          <div className=\"flex justify-between items-center\">\n            <div className=\"h-4 w-16 skeleton rounded\"></div>\n            <div className=\"h-6 w-24 skeleton rounded\"></div>\n          </div>\n          <div className=\"h-4 skeleton rounded\"></div>\n          <div className=\"h-10 skeleton rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <Link href={`/phone/${phone.id}`} className=\"block group\">\n      <div className=\"neu-card hover-lift overflow-hidden relative\">\n        {/* Phone Image */}\n        <div className=\"relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden\">\n          {phone.imageUrl ? (\n            <>\n              {!imageLoaded && (\n                <div className=\"absolute inset-0 skeleton\"></div>\n              )}\n              <Image\n                src={phone.imageUrl}\n                alt={phone.name}\n                fill\n                className={`object-cover transition-all duration-500 group-hover:scale-110 ${\n                  imageLoaded ? 'opacity-100' : 'opacity-0'\n                }`}\n                onLoad={() => setImageLoaded(true)}\n              />\n            </>\n          ) : (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-gray-400 text-center\">\n                <FiShoppingCart size={48} />\n                <p className=\"mt-2 font-medium\">No Image</p>\n              </div>\n            </div>\n          )}\n\n          {/* Badges */}\n          {badge && (\n            <div className={`absolute top-3 left-3 ${badge.class} z-10`}>\n              {badge.text}\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0\">\n            <button\n              onClick={handleLikeClick}\n              className={`p-2 glass rounded-full transition-all duration-300 hover-glow ${\n                isLiked ? 'text-red-500' : 'text-gray-600'\n              }`}\n            >\n              <FiHeart size={18} fill={isLiked ? 'currentColor' : 'none'} />\n            </button>\n            <button className=\"p-2 glass rounded-full transition-all duration-300 hover-glow text-gray-600\">\n              <FiEye size={18} />\n            </button>\n          </div>\n\n          {/* Quick Buy Overlay */}\n          <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center\">\n            <button\n              onClick={handleBuyClick}\n              className=\"gradient-gold text-black font-semibold py-3 px-6 rounded-full hover-glow transition-all duration-300 transform scale-90 group-hover:scale-100 flex items-center space-x-2\"\n            >\n              <FiShoppingCart size={18} />\n              <span>Quick Buy</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Phone Details */}\n        <div className=\"p-6\">\n          {/* Rating */}\n          <div className=\"flex items-center space-x-1 mb-2\">\n            {[...Array(5)].map((_, i) => (\n              <FiStar\n                key={i}\n                size={14}\n                className={i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'}\n              />\n            ))}\n            <span className=\"text-sm text-gray-500 ml-2\">(4.0)</span>\n          </div>\n\n          {/* Phone Name */}\n          <h3 className=\"text-lg font-bold text-gray-900 mb-2 group-hover:text-yellow-600 transition-colors duration-200 line-clamp-1\">\n            {phone.name}\n          </h3>\n\n          {/* Storage and Price */}\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium text-gray-600 bg-gray-100 px-3 py-1 rounded-full\">\n                {phone.storage}\n              </span>\n              {phone.name.toLowerCase().includes('5g') && (\n                <span className=\"text-xs font-bold text-blue-600 bg-blue-100 px-2 py-1 rounded-full flex items-center space-x-1\">\n                  <FiZap size={12} />\n                  <span>5G</span>\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* Price */}\n          <div className=\"mb-4\">\n            <div className=\"flex items-baseline space-x-2\">\n              <span className=\"text-2xl font-bold text-gradient\">\n                KSh {phone.price.toLocaleString()}\n              </span>\n              {phone.price > 150000 && (\n                <span className=\"text-sm text-gray-500 line-through\">\n                  KSh {(phone.price * 1.2).toLocaleString()}\n                </span>\n              )}\n            </div>\n            {phone.price > 150000 && (\n              <span className=\"text-sm text-green-600 font-medium\">Save 20%</span>\n            )}\n          </div>\n\n          {/* Description */}\n          {phone.description && (\n            <p className=\"text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed\">\n              {phone.description}\n            </p>\n          )}\n\n          {/* Buy Button */}\n          <button\n            onClick={handleBuyClick}\n            className=\"w-full neu-button text-gray-800 font-semibold py-3 px-4 transition-all duration-300 flex items-center justify-center space-x-2 hover:shadow-gold\"\n          >\n            <FiShoppingCart size={18} />\n            <span>Add to Cart</span>\n          </button>\n\n          {/* Features */}\n          <div className=\"mt-4 flex items-center justify-between text-xs text-gray-500\">\n            <span className=\"flex items-center space-x-1\">\n              <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n              <span>In Stock</span>\n            </span>\n            <span>Free Delivery</span>\n            <span>1 Year Warranty</span>\n          </div>\n        </div>\n      </div>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAce,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAkB;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,OAAO;YACT,MAAM;QACR,OAAO;YACL,+BAA+B;YAC/B,MAAM,UAAU,CAAC,0BAA0B,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,cAAc,IAAI;YACpH,MAAM,iBAAiB,oDAA2C;YAClE,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU;YACzF,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,WAAW,CAAC;IACd;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,IAAI,MAAM,QAAQ,EAAE,OAAO;YAAE,MAAM;YAAY,OAAO;QAAY;QAClE,IAAI,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,OAAO;YAAE,MAAM;YAAO,OAAO;QAAY;QACvF,IAAI,MAAM,KAAK,GAAG,QAAQ,OAAO;YAAE,MAAM;YAAQ,OAAO;QAAa;QACrE,OAAO;IACT;IAEA,MAAM,QAAQ;IAEd,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAAE,WAAU;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,QAAQ,iBACb;;gCACG,CAAC,6BACA,8OAAC;oCAAI,WAAU;;;;;;8CAEjB,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,QAAQ;oCACnB,KAAK,MAAM,IAAI;oCACf,IAAI;oCACJ,WAAW,CAAC,+DAA+D,EACzE,cAAc,gBAAgB,aAC9B;oCACF,QAAQ,IAAM,eAAe;;;;;;;yDAIjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,MAAM;;;;;;kDACtB,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;;;;;;wBAMrC,uBACC,8OAAC;4BAAI,WAAW,CAAC,sBAAsB,EAAE,MAAM,KAAK,CAAC,KAAK,CAAC;sCACxD,MAAM,IAAI;;;;;;sCAKf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,8DAA8D,EACxE,UAAU,iBAAiB,iBAC3B;8CAEF,cAAA,8OAAC,8IAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,MAAM,UAAU,iBAAiB;;;;;;;;;;;8CAEtD,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,8IAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,MAAM;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,8IAAA,CAAA,SAAM;wCAEL,MAAM;wCACN,WAAW,IAAI,IAAI,iCAAiC;uCAF/C;;;;;8CAKT,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;sCAI/C,8OAAC;4BAAG,WAAU;sCACX,MAAM,IAAI;;;;;;sCAIb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,MAAM,OAAO;;;;;;oCAEf,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,uBACjC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,8IAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;0DACb,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAmC;gDAC5C,MAAM,KAAK,CAAC,cAAc;;;;;;;wCAEhC,MAAM,KAAK,GAAG,wBACb,8OAAC;4CAAK,WAAU;;gDAAqC;gDAC9C,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE,cAAc;;;;;;;;;;;;;gCAI5C,MAAM,KAAK,GAAG,wBACb,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;;wBAKxD,MAAM,WAAW,kBAChB,8OAAC;4BAAE,WAAU;sCACV,MAAM,WAAW;;;;;;sCAKtB,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,MAAM;;;;;;8CACtB,8OAAC;8CAAK;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/.vscode/Projects/highendphones/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ref, onValue } from 'firebase/database';\nimport { database } from '@/lib/firebase';\nimport { Phone } from '@/types/phone';\nimport Navigation from '@/components/Navigation';\nimport HeroCarousel from '@/components/HeroCarousel';\nimport PhoneCard from '@/components/PhoneCard';\nimport { FiMapPin, FiPhone, FiMail, FiClock } from 'react-icons/fi';\n\nexport default function Home() {\n  const [phones, setPhones] = useState<Phone[]>([]);\n  const [filteredPhones, setFilteredPhones] = useState<Phone[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [sortBy, setSortBy] = useState<string>('featured');\n\n  // Fetch phones from Firebase\n  useEffect(() => {\n    const phonesRef = ref(database, 'phones');\n    const unsubscribe = onValue(phonesRef, (snapshot) => {\n      const data = snapshot.val();\n      if (data) {\n        const phonesList = Object.keys(data).map(key => ({\n          id: key,\n          ...data[key]\n        }));\n        setPhones(phonesList);\n        setFilteredPhones(phonesList);\n      } else {\n        // Demo data if no Firebase data\n        const demoPhones: Phone[] = [\n          {\n            id: '1',\n            name: 'iPhone 15 Pro Max',\n            storage: '256GB',\n            price: 180000,\n            description: 'Latest iPhone with titanium design and advanced camera system',\n            imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',\n            featured: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '2',\n            name: 'Samsung Galaxy S24 Ultra',\n            storage: '512GB',\n            price: 165000,\n            description: 'Premium Android flagship with S Pen and incredible camera zoom',\n            imageUrl: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',\n            featured: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '3',\n            name: 'Google Pixel 8 Pro',\n            storage: '128GB',\n            price: 120000,\n            description: 'Pure Android experience with exceptional AI photography',\n            imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          },\n          {\n            id: '4',\n            name: 'OnePlus 12',\n            storage: '256GB',\n            price: 95000,\n            description: 'Fast charging flagship with premium performance',\n            imageUrl: 'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=500',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          }\n        ];\n        setPhones(demoPhones);\n        setFilteredPhones(demoPhones);\n      }\n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  // Filter and sort phones\n  useEffect(() => {\n    let filtered = phones;\n\n    // Apply search filter\n    if (searchQuery.trim() !== '') {\n      filtered = filtered.filter(phone =>\n        phone.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        phone.storage.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        phone.description?.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    // Apply category filter\n    if (selectedCategory && selectedCategory !== 'Deals') {\n      filtered = filtered.filter(phone =>\n        phone.name.toLowerCase().includes(selectedCategory.toLowerCase())\n      );\n    } else if (selectedCategory === 'Deals') {\n      filtered = filtered.filter(phone => phone.price < 100000);\n    }\n\n    // Apply sorting\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      case 'featured':\n      default:\n        filtered.sort((a, b) => {\n          if (a.featured && !b.featured) return -1;\n          if (!a.featured && b.featured) return 1;\n          return 0;\n        });\n        break;\n    }\n\n    setFilteredPhones(filtered);\n  }, [searchQuery, selectedCategory, sortBy, phones]);\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n  };\n\n  const handleCategoryFilter = (category: string) => {\n    setSelectedCategory(category === selectedCategory ? '' : category);\n  };\n\n  const featuredPhones = phones.filter(phone => phone.featured);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation onSearch={handleSearch} onCategoryFilter={handleCategoryFilter} />\n\n      {/* Hero Section */}\n      <HeroCarousel featuredPhones={featuredPhones} />\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              About High End Phones.ke\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n              We are Kenya's premier destination for high-end smartphones. Our commitment to quality,\n              competitive pricing, and exceptional customer service makes us the trusted choice for\n              premium mobile devices.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiPhone className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Premium Quality</h3>\n              <p className=\"text-gray-600\">Only the finest smartphones from trusted brands</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiMapPin className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Kenya-wide Delivery</h3>\n              <p className=\"text-gray-600\">Fast and secure delivery across Kenya</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 gradient-gold rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FiClock className=\"text-black text-2xl\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">24/7 Support</h3>\n              <p className=\"text-gray-600\">Round-the-clock customer support</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Phones Section */}\n      <section id=\"phones\" className=\"py-20 bg-gradient-to-br from-gray-50 to-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Our Premium Collection\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover the latest high-end smartphones with cutting-edge technology and unmatched performance\n            </p>\n          </div>\n\n          {/* Filters and Sort */}\n          <div className=\"flex flex-col lg:flex-row justify-between items-center mb-12 space-y-4 lg:space-y-0\">\n            {/* Category Pills */}\n            <div className=\"flex flex-wrap gap-3\">\n              {['iPhone', 'Samsung', 'Google', 'OnePlus', 'Deals'].map((category) => (\n                <button\n                  key={category}\n                  onClick={() => handleCategoryFilter(category)}\n                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${\n                    selectedCategory === category\n                      ? 'gradient-gold text-black shadow-gold'\n                      : 'glass hover:bg-white/20 text-gray-700'\n                  }`}\n                >\n                  {category === 'Deals' ? '🔥 ' : ''}\n                  {category}\n                </button>\n              ))}\n              {selectedCategory && (\n                <button\n                  onClick={() => setSelectedCategory('')}\n                  className=\"px-4 py-3 rounded-full text-gray-500 hover:text-gray-700 transition-colors duration-300\"\n                >\n                  ✕ Clear\n                </button>\n              )}\n            </div>\n\n            {/* Sort Dropdown */}\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-gray-600 font-medium\">Sort by:</span>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"glass rounded-lg px-4 py-2 font-medium focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\n              >\n                <option value=\"featured\">Featured</option>\n                <option value=\"price-low\">Price: Low to High</option>\n                <option value=\"price-high\">Price: High to Low</option>\n                <option value=\"name\">Name A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Results Count */}\n          {!loading && (\n            <div className=\"mb-8\">\n              <p className=\"text-gray-600\">\n                {selectedCategory && (\n                  <span className=\"font-medium text-yellow-600\">{selectedCategory} • </span>\n                )}\n                {filteredPhones.length} phone{filteredPhones.length !== 1 ? 's' : ''} found\n                {searchQuery && (\n                  <span className=\"ml-2 text-sm\">\n                    for \"<span className=\"font-medium\">{searchQuery}</span>\"\n                  </span>\n                )}\n              </p>\n            </div>\n          )}\n\n          {/* Loading State */}\n          {loading ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n              {[...Array(8)].map((_, i) => (\n                <PhoneCard key={i} phone={{} as Phone} isLoading={true} />\n              ))}\n            </div>\n          ) : (\n            <>\n              {/* Phones Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n                {filteredPhones.map((phone) => (\n                  <PhoneCard key={phone.id} phone={phone} />\n                ))}\n              </div>\n\n              {/* Empty State */}\n              {filteredPhones.length === 0 && (\n                <div className=\"text-center py-20\">\n                  <div className=\"max-w-md mx-auto\">\n                    <div className=\"text-6xl mb-6\">📱</div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">No phones found</h3>\n                    <p className=\"text-gray-600 mb-8\">\n                      {searchQuery || selectedCategory\n                        ? \"Try adjusting your search or filters to find what you're looking for.\"\n                        : \"We're currently updating our inventory. Please check back soon!\"}\n                    </p>\n                    {(searchQuery || selectedCategory) && (\n                      <button\n                        onClick={() => {\n                          setSearchQuery('');\n                          setSelectedCategory('');\n                        }}\n                        className=\"gradient-gold text-black font-semibold py-3 px-8 rounded-full hover-glow transition-all duration-300\"\n                      >\n                        Clear All Filters\n                      </button>\n                    )}\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Get in Touch\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Ready to upgrade your phone? Contact us today!\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Contact Information</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <FiPhone className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\">+254 700 000 000</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <FiMail className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <FiMapPin className=\"text-yellow-600 text-xl\" />\n                  <span className=\"text-gray-700\">Nairobi, Kenya</span>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Business Hours</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Monday - Friday</span>\n                  <span className=\"text-gray-900 font-medium\">9:00 AM - 6:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Saturday</span>\n                  <span className=\"text-gray-900 font-medium\">10:00 AM - 4:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-700\">Sunday</span>\n                  <span className=\"text-gray-900 font-medium\">Closed</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-black text-white py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-4\">High End Phones.ke</h3>\n            <p className=\"text-gray-400 mb-4\">\n              Your trusted partner for premium smartphones in Kenya\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 High End Phones.ke. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,WAAQ,EAAE;QAChC,MAAM,cAAc,CAAA,GAAA,qLAAA,CAAA,UAAO,AAAD,EAAE,WAAW,CAAC;YACtC,MAAM,OAAO,SAAS,GAAG;YACzB,IAAI,MAAM;gBACR,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC/C,IAAI;wBACJ,GAAG,IAAI,CAAC,IAAI;oBACd,CAAC;gBACD,UAAU;gBACV,kBAAkB;YACpB,OAAO;gBACL,gCAAgC;gBAChC,MAAM,aAAsB;oBAC1B;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;iBACD;gBACD,UAAU;gBACV,kBAAkB;YACpB;YACA,WAAW;QACb;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,YAAY,IAAI,OAAO,IAAI;YAC7B,WAAW,SAAS,MAAM,CAAC,CAAA,QACzB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,MAAM,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW;QAErE;QAEA,wBAAwB;QACxB,IAAI,oBAAoB,qBAAqB,SAAS;YACpD,WAAW,SAAS,MAAM,CAAC,CAAA,QACzB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,iBAAiB,WAAW;QAElE,OAAO,IAAI,qBAAqB,SAAS;YACvC,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;QACpD;QAEA,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACnD;YACF,KAAK;YACL;gBACE,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;oBACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;oBACtC,OAAO;gBACT;gBACA;QACJ;QAEA,kBAAkB;IACpB,GAAG;QAAC;QAAa;QAAkB;QAAQ;KAAO;IAElD,MAAM,eAAe,CAAC;QACpB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB,aAAa,mBAAmB,KAAK;IAC3D;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;gBAAC,UAAU;gBAAc,kBAAkB;;;;;;0BAGtD,8OAAC,kIAAA,CAAA,UAAY;gBAAC,gBAAgB;;;;;;0BAG9B,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ;4CAAC;4CAAU;4CAAW;4CAAU;4CAAW;yCAAQ,CAAC,GAAG,CAAC,CAAC,yBACxD,8OAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,CAAC,+DAA+D,EACzE,qBAAqB,WACjB,yCACA,yCACJ;;oDAED,aAAa,UAAU,QAAQ;oDAC/B;;+CATI;;;;;wCAYR,kCACC,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,8OAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;wBAM1B,CAAC,yBACA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCACV,kCACC,8OAAC;wCAAK,WAAU;;4CAA+B;4CAAiB;;;;;;;oCAEjE,eAAe,MAAM;oCAAC;oCAAO,eAAe,MAAM,KAAK,IAAI,MAAM;oCAAG;oCACpE,6BACC,8OAAC;wCAAK,WAAU;;4CAAe;0DACxB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;4CAAmB;;;;;;;;;;;;;;;;;;wBAQhE,wBACC,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,+HAAA,CAAA,UAAS;oCAAS,OAAO,CAAC;oCAAY,WAAW;mCAAlC;;;;;;;;;iDAIpB;;8CAEE,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,+HAAA,CAAA,UAAS;4CAAgB,OAAO;2CAAjB,MAAM,EAAE;;;;;;;;;;gCAK3B,eAAe,MAAM,KAAK,mBACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DACV,eAAe,mBACZ,0EACA;;;;;;4CAEL,CAAC,eAAe,gBAAgB,mBAC/B,8OAAC;gDACC,SAAS;oDACP,eAAe;oDACf,oBAAoB;gDACtB;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAKtC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}