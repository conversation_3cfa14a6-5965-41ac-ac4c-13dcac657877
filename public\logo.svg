<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500">
  <defs>
    <radialGradient id="phoneGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="250" cy="250" r="200" fill="url(#phoneGradient)"/>
  
  <!-- Small decorative dots -->
  <circle cx="150" cy="100" r="8" fill="#000"/>
  <circle cx="350" cy="120" r="12" fill="#000"/>
  <circle cx="220" cy="400" r="10" fill="#FFD700"/>
  
  <!-- Phone outline -->
  <rect x="180" y="150" width="140" height="200" rx="20" ry="20" fill="none" stroke="#FFF" stroke-width="4"/>
  
  <!-- Phone screen -->
  <rect x="190" y="170" width="120" height="160" rx="10" ry="10" fill="url(#phoneGradient)" opacity="0.8"/>
  
  <!-- Location pin -->
  <path d="M120 180 C120 160, 140 140, 160 140 C180 140, 200 160, 200 180 C200 200, 160 240, 160 240 C160 240, 120 200, 120 180 Z" fill="#FFD700"/>
  <circle cx="160" cy="180" r="15" fill="#FFF"/>
</svg>
