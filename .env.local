# Firebase Configuration (Demo - replace with your actual config)
NEXT_PUBLIC_FIREBASE_API_KEY=demo-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=demo-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://demo-project-default-rtdb.firebaseio.com/
NEXT_PUBLIC_FIREBASE_PROJECT_ID=demo-project
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=demo-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# const firebaseConfig = {
#   apiKey: "AIzaSyDGf56DgK9zZeYr9fHrNhgF4pytP9oRCuQ",
#   authDomain: "ndungu-shop.firebaseapp.com",
#   projectId: "ndungu-shop",
#   storageBucket: "ndungu-shop.firebasestorage.app",
#   messagingSenderId: "922809103168",
#   appId: "1:922809103168:web:e1fb4253bf5bd1c57540a5",
#   measurementId: "G-8WR28F09RF"
# };

# Cloudinary Configuration (Demo - replace with your actual config)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=du02pkrhf
CLOUDINARY_API_KEY=592135841396749
CLOUDINARY_API_SECRET=GWV3zVxrV_h8C6DBryBtOFyS93Y

# WhatsApp Configuration
NEXT_PUBLIC_WHATSAPP_NUMBER=254700000000
